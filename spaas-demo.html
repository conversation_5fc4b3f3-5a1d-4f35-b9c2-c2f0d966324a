<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - SIP Platform as a Service</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #dbeafe 0%, #ffffff 50%, #e0e7ff 100%);
        }
    </style>
</head>
<body class="gradient-bg">
    <!-- Header -->
    <header class="bg-white/80 backdrop-blur-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-900">SPaaS Platform</h1>
                    <span class="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">Beta</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="signInBtn" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        Sign In
                    </button>
                    <button id="dashboardBtn" class="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
                        Dashboard Demo
                    </button>
                    <button id="getStartedBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                SIP Platform as a Service
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                Complete multi-tenant telecommunications platform with AI-powered call flows, 
                real-time analytics, and seamless integrations. Built for resellers and enterprises.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button id="trialBtn" class="px-8 py-3 bg-blue-600 text-white rounded-md text-lg font-medium hover:bg-blue-700">
                    Start Free Trial
                </button>
                <button id="dashboardBtn2" class="px-8 py-3 bg-green-600 text-white rounded-md text-lg font-medium hover:bg-green-700">
                    Dashboard Demo
                </button>
                <button id="featuresBtn" class="px-8 py-3 border border-gray-300 text-gray-700 rounded-md text-lg font-medium hover:bg-gray-50">
                    View Features
                </button>
            </div>
        </div>
    </section>

    <!-- Features Grid -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Everything you need for modern telephony
            </h2>
            <p class="text-lg text-gray-600">
                Powerful features designed for scalability and ease of use
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Multi-Tenant Architecture -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Multi-Tenant Architecture</h3>
                <p class="text-gray-600">
                    Complete tenant isolation with white-label capabilities and custom branding
                </p>
            </div>

            <!-- SIP & VoIP Management -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">SIP & VoIP Management</h3>
                <p class="text-gray-600">
                    Full SIP account management with DID numbers, call routing, and recording
                </p>
            </div>

            <!-- AI-Powered IVR -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">AI-Powered IVR</h3>
                <p class="text-gray-600">
                    Visual workflow builder with ChatGPT integration for intelligent call flows
                </p>
            </div>

            <!-- Real-time Analytics -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Real-time Analytics</h3>
                <p class="text-gray-600">
                    Comprehensive reporting with CDR analysis and usage statistics
                </p>
            </div>

            <!-- Enterprise Security -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Enterprise Security</h3>
                <p class="text-gray-600">
                    Row-level security, JWT authentication, and comprehensive audit logging
                </p>
            </div>

            <!-- Global Integrations -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Global Integrations</h3>
                <p class="text-gray-600">
                    SignalWire, Twilio, Stripe integration with webhook support
                </p>
            </div>
        </div>
    </section>

    <!-- Database Schema Section -->
    <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                Robust Database Architecture
            </h2>
            <p class="text-lg text-gray-600">
                15 interconnected tables with full multi-tenancy support
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h4 class="font-semibold text-gray-900">Core Tables</h4>
                    <ul class="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• Tenants (Organizations)</li>
                        <li>• Users (Multi-role)</li>
                        <li>• Clients (End customers)</li>
                    </ul>
                </div>
                <div class="border-l-4 border-green-500 pl-4">
                    <h4 class="font-semibold text-gray-900">Telephony</h4>
                    <ul class="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• SIP Accounts</li>
                        <li>• DID Numbers</li>
                        <li>• Call Flows (IVR)</li>
                        <li>• Call Detail Records</li>
                    </ul>
                </div>
                <div class="border-l-4 border-purple-500 pl-4">
                    <h4 class="font-semibold text-gray-900">Business</h4>
                    <ul class="text-sm text-gray-600 mt-2 space-y-1">
                        <li>• Invoices & Billing</li>
                        <li>• Support Tickets</li>
                        <li>• Audit Logs</li>
                        <li>• Webhooks</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-gray-900 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold mb-4">
                Ready to transform your telecommunications business?
            </h2>
            <p class="text-xl text-gray-300 mb-8">
                Join hundreds of companies already using SPaaS Platform
            </p>
            <button class="px-8 py-3 bg-white text-gray-900 rounded-md text-lg font-medium hover:bg-gray-100">
                Start Your Free Trial
            </button>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white border-t">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center text-gray-600">
                <p>&copy; 2024 SPaaS Platform. Built with Supabase, Next.js, and modern web technologies.</p>
                <p class="mt-2 text-sm">Complete source code available • Multi-tenant • Production-ready</p>
            </div>
        </div>
    </footer>

    <script>
        // Sign In functionality
        document.getElementById('signInBtn').addEventListener('click', function() {
            const email = prompt('Enter your email for magic link:');
            if (email) {
                alert(`🚀 SPaaS Platform Demo\n\n✅ Magic link would be sent to:\n${email}\n\n✅ Supabase Authentication Ready\n✅ OAuth Providers Configured\n✅ JWT Token Management\n\nFor demo purposes, click "Dashboard Demo" to continue!`);
            }
        });

        // Dashboard Demo functionality
        function openDashboard() {
            window.location.href = 'spaas-dashboard.html';
        }

        document.getElementById('dashboardBtn').addEventListener('click', openDashboard);
        document.getElementById('dashboardBtn2').addEventListener('click', openDashboard);

        // Get Started functionality
        document.getElementById('getStartedBtn').addEventListener('click', function() {
            alert('🚀 SPaaS Platform Demo\n\n✅ Google OAuth Integration Ready\n✅ Multi-tenant Architecture\n✅ Row-Level Security\n✅ Complete Database Schema\n\nIn production, you would be redirected to Google login.\n\nClick "Dashboard Demo" to see the admin interface!');
        });

        document.getElementById('trialBtn').addEventListener('click', function() {
            alert('🚀 Start Your Free Trial\n\n✅ 30-day free trial\n✅ Full platform access\n✅ Multi-tenant setup\n✅ Technical support\n\nClick "Dashboard Demo" to explore the interface!');
        });

        // Features functionality
        document.getElementById('featuresBtn').addEventListener('click', function() {
            alert('🚀 SPaaS Platform Features\n\n✅ Multi-tenant architecture\n✅ SIP/VoIP management\n✅ AI-powered IVR builder\n✅ Real-time analytics\n✅ Enterprise security\n✅ Global integrations\n✅ 15 database tables\n✅ Edge functions\n\nClick "Dashboard Demo" to see it in action!');
        });

        // CTA button
        document.querySelector('section.bg-gray-900 button').addEventListener('click', function() {
            alert('🚀 Transform Your Business\n\n✅ Complete SPaaS solution\n✅ White-label ready\n✅ Scalable architecture\n✅ Production-ready code\n\nClick "Dashboard Demo" to explore!');
        });
    </script>
</body>
</html>
