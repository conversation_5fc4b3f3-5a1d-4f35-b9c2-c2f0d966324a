<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Analytics & Reports</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        * { font-family: 'Inter', sans-serif; }
        .sidebar-item { transition: all 0.2s ease; }
        .sidebar-item:hover { background-color: #f3f4f6; }
        .sidebar-item.active { background-color: #3b82f6; color: white; }
        .chart-placeholder { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">SPaaS</h1>
                </div>
                <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">A</div>
                    <span class="text-sm font-medium text-gray-700">Acme Telecom</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="goHome()" class="text-sm text-gray-600 hover:text-gray-900">← Back to Home</button>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AJ</div>
                        <div class="ml-3 hidden md:block">
                            <div class="text-sm font-medium text-gray-700">Alex Johnson</div>
                            <div class="text-xs text-gray-500"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <nav class="w-64 bg-white border-r border-gray-200 fixed h-full overflow-y-auto">
            <div class="p-4">
                <ul class="space-y-1">
                    <li><a href="spaas-dashboard-professional.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>Dashboard</a></li>
                    <li><a href="spaas-clients.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>Clients</a></li>
                    <li><a href="spaas-sip-accounts.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>SIP Accounts</a></li>
                    <li><a href="spaas-call-flows.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>Call Flows</a></li>
                    <li><a href="spaas-support.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>Support</a></li>
                    <li><a href="spaas-billing.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>Billing</a></li>
                    <li><a href="#" class="sidebar-item active flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>Reports</a></li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 ml-64 p-8">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Analytics & Reports</h1>
                        <p class="mt-1 text-sm text-gray-500">Comprehensive insights into your telecommunications platform performance</p>
                    </div>
                    <div class="flex space-x-3">
                        <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>Last 7 days</option>
                            <option>Last 30 days</option>
                            <option>Last 90 days</option>
                            <option>Custom range</option>
                        </select>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                            Export Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Total Calls</p>
                            <p class="text-2xl font-semibold text-gray-900">12,847</p>
                            <p class="text-xs text-green-600 mt-1">+18.2% from last period</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Avg Call Duration</p>
                            <p class="text-2xl font-semibold text-gray-900">4:32</p>
                            <p class="text-xs text-blue-600 mt-1">+0:15 from last period</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Success Rate</p>
                            <p class="text-2xl font-semibold text-gray-900">98.7%</p>
                            <p class="text-xs text-green-600 mt-1">+0.3% from last period</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Revenue Impact</p>
                            <p class="text-2xl font-semibold text-gray-900">$24.8K</p>
                            <p class="text-xs text-green-600 mt-1">+12.5% from last period</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Call Volume Chart -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Call Volume Trends</h3>
                        <select class="text-sm border border-gray-300 rounded px-2 py-1">
                            <option>Daily</option>
                            <option>Weekly</option>
                            <option>Monthly</option>
                        </select>
                    </div>
                    <div class="chart-placeholder h-64">
                        📈 Call Volume Chart
                        <br><small>Real-time call analytics</small>
                    </div>
                </div>

                <!-- Geographic Distribution -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium text-gray-900">Geographic Distribution</h3>
                        <button class="text-sm text-blue-600 hover:text-blue-700">View Details</button>
                    </div>
                    <div class="chart-placeholder h-64">
                        🗺️ Geographic Map
                        <br><small>Call distribution by region</small>
                    </div>
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                <!-- Top Performing Clients -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Top Performing Clients</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium mr-3">TC</div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">TechCorp Solutions</div>
                                    <div class="text-xs text-gray-500">4,247 calls</div>
                                </div>
                            </div>
                            <div class="text-sm font-semibold text-green-600">+15%</div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-medium mr-3">GI</div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">Global Industries</div>
                                    <div class="text-xs text-gray-500">3,891 calls</div>
                                </div>
                            </div>
                            <div class="text-sm font-semibold text-green-600">+8%</div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-medium mr-3">SS</div>
                                <div>
                                    <div class="text-sm font-medium text-gray-900">StartupCo</div>
                                    <div class="text-xs text-gray-500">1,247 calls</div>
                                </div>
                            </div>
                            <div class="text-sm font-semibold text-blue-600">+22%</div>
                        </div>
                    </div>
                </div>

                <!-- Call Quality Metrics -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Call Quality Metrics</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Audio Quality</span>
                            <div class="flex items-center">
                                <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 94%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">94%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Connection Stability</span>
                            <div class="flex items-center">
                                <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 97%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">97%</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Latency</span>
                            <div class="flex items-center">
                                <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">78ms</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Packet Loss</span>
                            <div class="flex items-center">
                                <div class="w-24 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 99%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">0.1%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3"></div>
                            <div>
                                <div class="text-sm text-gray-900">New SIP account created</div>
                                <div class="text-xs text-gray-500">TechCorp Solutions • 2 hours ago</div>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></div>
                            <div>
                                <div class="text-sm text-gray-900">Call flow updated</div>
                                <div class="text-xs text-gray-500">Global Industries • 4 hours ago</div>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-purple-500 rounded-full mt-2 mr-3"></div>
                            <div>
                                <div class="text-sm text-gray-900">Invoice generated</div>
                                <div class="text-xs text-gray-500">StartupCo • 6 hours ago</div>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3"></div>
                            <div>
                                <div class="text-sm text-gray-900">Support ticket resolved</div>
                                <div class="text-xs text-gray-500">TechCorp Solutions • 8 hours ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function goHome() {
            window.location.href = 'spaas-professional.html';
        }

        // Initialize analytics functionality
        document.addEventListener('DOMContentLoaded', function() {
            setupAnalyticsHandlers();
        });

        function setupAnalyticsHandlers() {
            // Export Report button
            const exportBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Export Report'));
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    alert('📊 Export Analytics Report\n\n✓ Comprehensive call analytics\n✓ Revenue and billing data\n✓ Client performance metrics\n✓ Quality and uptime stats\n✓ Custom date ranges\n✓ Multiple formats (PDF, Excel, CSV)\n\nGenerating detailed report...');
                });
            }

            // Chart placeholders click handlers
            document.querySelectorAll('.chart-placeholder').forEach(chart => {
                chart.addEventListener('click', function() {
                    const chartType = this.textContent.trim().split('\n')[0];
                    alert(`📈 ${chartType}\n\n✓ Interactive data visualization\n✓ Real-time updates\n✓ Drill-down capabilities\n✓ Export chart data\n✓ Custom time ranges\n✓ Multi-tenant filtering\n\nThis would show detailed analytics with Chart.js or D3.js integration.`);
                });
            });

            // Time range selector
            const timeRangeSelectors = document.querySelectorAll('select');
            timeRangeSelectors.forEach(selector => {
                selector.addEventListener('change', function() {
                    const selectedRange = this.value;
                    alert(`📅 Time Range Updated\n\nSelected: ${selectedRange}\n\n✓ Data refreshed for new range\n✓ All charts updated\n✓ Metrics recalculated\n✓ Comparisons adjusted\n\nAnalytics data updated successfully!`);
                });
            });

            // View Details buttons
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent.includes('View Details')) {
                    btn.addEventListener('click', function() {
                        alert('🗺️ Geographic Analytics Details\n\n✓ Call distribution by country/region\n✓ Peak usage times by timezone\n✓ Quality metrics by location\n✓ Cost analysis by region\n✓ Carrier performance by area\n\nThis would open detailed geographic analytics.');
                    });
                }
            });
        }
    </script>
</body>
</html>
