// Stripe Webhook Handler for SPaaS Platform
// Handles subscription events, payment processing, and invoice updates

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'jsr:@supabase/supabase-js@2'

interface StripeEvent {
  id: string
  type: string
  data: {
    object: any
  }
  created: number
}

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const STRIPE_WEBHOOK_SECRET = Deno.env.get('STRIPE_WEBHOOK_SECRET') ?? ''

Deno.serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const body = await req.text()
    const signature = req.headers.get('stripe-signature')

    if (!signature) {
      return new Response('Missing signature', { status: 400 })
    }

    // Verify webhook signature (simplified - in production use proper Stripe verification)
    const event: StripeEvent = JSON.parse(body)

    console.log('Stripe webhook event:', event.type)

    switch (event.type) {
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event)
        break

      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event)
        break

      case 'customer.subscription.created':
        await handleSubscriptionCreated(event)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event)
        break

      default:
        console.log('Unhandled event type:', event.type)
    }

    return new Response('OK', { status: 200 })
  } catch (error) {
    console.error('Stripe webhook error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
})

async function handleInvoicePaymentSucceeded(event: StripeEvent) {
  const invoice = event.data.object

  console.log('Invoice payment succeeded:', invoice.id)

  // Update invoice status in database
  const { error } = await supabase
    .from('invoices')
    .update({
      status: 'paid',
      paid_at: new Date().toISOString(),
      payment_method: 'stripe',
      payment_reference: invoice.payment_intent
    })
    .eq('stripe_invoice_id', invoice.id)

  if (error) {
    console.error('Error updating invoice:', error)
  }

  // Update subscription if this is a subscription invoice
  if (invoice.subscription) {
    const { error: subError } = await supabase
      .from('subscriptions')
      .update({
        status: 'active',
        current_period_start: new Date(invoice.period_start * 1000).toISOString().split('T')[0],
        current_period_end: new Date(invoice.period_end * 1000).toISOString().split('T')[0]
      })
      .eq('stripe_subscription_id', invoice.subscription)

    if (subError) {
      console.error('Error updating subscription:', subError)
    }
  }
}

async function handleInvoicePaymentFailed(event: StripeEvent) {
  const invoice = event.data.object

  console.log('Invoice payment failed:', invoice.id)

  // Update invoice status
  const { error } = await supabase
    .from('invoices')
    .update({
      status: 'overdue'
    })
    .eq('stripe_invoice_id', invoice.id)

  if (error) {
    console.error('Error updating invoice:', error)
  }

  // Optionally suspend subscription after multiple failed payments
  if (invoice.attempt_count >= 3) {
    const { error: subError } = await supabase
      .from('subscriptions')
      .update({
        status: 'past_due'
      })
      .eq('stripe_subscription_id', invoice.subscription)

    if (subError) {
      console.error('Error updating subscription:', subError)
    }
  }
}

async function handleSubscriptionCreated(event: StripeEvent) {
  const subscription = event.data.object

  console.log('Subscription created:', subscription.id)

  // Find customer and create subscription record
  const customerId = subscription.customer

  // In a real implementation, you'd need to map Stripe customer ID to your tenant/client
  // For now, we'll log the event
  console.log('New subscription for customer:', customerId)
}

async function handleSubscriptionUpdated(event: StripeEvent) {
  const subscription = event.data.object

  console.log('Subscription updated:', subscription.id)

  const { error } = await supabase
    .from('subscriptions')
    .update({
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString().split('T')[0],
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString().split('T')[0],
      trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString().split('T')[0] : null
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription:', error)
  }
}

async function handleSubscriptionDeleted(event: StripeEvent) {
  const subscription = event.data.object

  console.log('Subscription deleted:', subscription.id)

  const { error } = await supabase
    .from('subscriptions')
    .update({
      status: 'cancelled',
      cancelled_at: new Date().toISOString()
    })
    .eq('stripe_subscription_id', subscription.id)

  if (error) {
    console.error('Error updating subscription:', error)
  }
}

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/stripe-webhook' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
