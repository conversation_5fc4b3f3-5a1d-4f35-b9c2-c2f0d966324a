<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Support Tickets</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        * { font-family: 'Inter', sans-serif; }
        .sidebar-item { transition: all 0.2s ease; }
        .sidebar-item:hover { background-color: #f3f4f6; }
        .sidebar-item.active { background-color: #3b82f6; color: white; }
        .ticket-card { transition: all 0.2s ease; cursor: pointer; }
        .ticket-card:hover { box-shadow: 0 4px 12px rgba(0,0,0,0.1); transform: translateY(-1px); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">SPaaS</h1>
                </div>
                <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">A</div>
                    <span class="text-sm font-medium text-gray-700">Acme Telecom</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="goHome()" class="text-sm text-gray-600 hover:text-gray-900">← Back to Home</button>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AJ</div>
                        <div class="ml-3 hidden md:block">
                            <div class="text-sm font-medium text-gray-700">Alex Johnson</div>
                            <div class="text-xs text-gray-500"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <nav class="w-64 bg-white border-r border-gray-200 fixed h-full overflow-y-auto">
            <div class="p-4">
                <ul class="space-y-1">
                    <li><a href="spaas-dashboard-professional.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>Dashboard</a></li>
                    <li><a href="spaas-clients.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>Clients</a></li>
                    <li><a href="spaas-sip-accounts.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>SIP Accounts</a></li>
                    <li><a href="spaas-call-flows.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>Call Flows</a></li>
                    <li><a href="spaas-billing.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>Billing</a></li>
                    <li><a href="#" class="sidebar-item active flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>Support</a></li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 ml-64 p-8">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Support Tickets</h1>
                        <p class="mt-1 text-sm text-gray-500">Manage customer support requests and technical issues</p>
                    </div>
                    <div class="flex space-x-3">
                        <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                            Export Tickets
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                            + New Ticket
                        </button>
                    </div>
                </div>
            </div>

            <!-- Support Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Open Tickets</p>
                            <p class="text-2xl font-semibold text-gray-900">7</p>
                            <p class="text-xs text-orange-600 mt-1">2 high priority</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Resolved This Month</p>
                            <p class="text-2xl font-semibold text-gray-900">42</p>
                            <p class="text-xs text-green-600 mt-1">+15% from last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Avg Response Time</p>
                            <p class="text-2xl font-semibold text-gray-900">2.4h</p>
                            <p class="text-xs text-green-600 mt-1">-0.3h from last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Customer Satisfaction</p>
                            <p class="text-2xl font-semibold text-gray-900">4.8</p>
                            <p class="text-xs text-purple-600 mt-1">out of 5.0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" placeholder="Search tickets..." class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>All Status</option>
                        <option>Open</option>
                        <option>In Progress</option>
                        <option>Resolved</option>
                        <option>Closed</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>All Priority</option>
                        <option>High</option>
                        <option>Medium</option>
                        <option>Low</option>
                    </select>
                    <select class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>All Clients</option>
                        <option>TechCorp Solutions</option>
                        <option>Global Industries</option>
                        <option>StartupCo</option>
                    </select>
                </div>
            </div>

            <!-- Tickets List -->
            <div class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Support Tickets</h3>
                </div>
                <div class="divide-y divide-gray-200">
                    <div class="ticket-card p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center text-white font-medium mr-4 mt-1">
                                    !
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-semibold text-gray-900">SIP Registration Issues</h4>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">High Priority</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Client unable to register SIP accounts after recent configuration changes</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span>TechCorp Solutions</span>
                                        <span class="mx-2">•</span>
                                        <span>Ticket #SUP-2024-001</span>
                                        <span class="mx-2">•</span>
                                        <span>Created 2 hours ago</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">Open</span>
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    AJ
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ticket-card p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center text-white font-medium mr-4 mt-1">
                                    ?
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-semibold text-gray-900">Call Flow Configuration Help</h4>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Medium Priority</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Need assistance setting up AI agent integration in call flow</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span>Global Industries</span>
                                        <span class="mx-2">•</span>
                                        <span>Ticket #SUP-2024-002</span>
                                        <span class="mx-2">•</span>
                                        <span>Created 5 hours ago</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">In Progress</span>
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    SM
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="ticket-card p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white font-medium mr-4 mt-1">
                                    ✓
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <h4 class="text-sm font-semibold text-gray-900">Billing Invoice Question</h4>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Low Priority</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Question about charges on latest invoice</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span>StartupCo</span>
                                        <span class="mx-2">•</span>
                                        <span>Ticket #SUP-2024-003</span>
                                        <span class="mx-2">•</span>
                                        <span>Resolved 1 day ago</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Resolved</span>
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                                    JD
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function goHome() {
            window.location.href = 'spaas-professional.html';
        }

        // Initialize support functionality
        document.addEventListener('DOMContentLoaded', function() {
            setupSupportHandlers();
        });

        function setupSupportHandlers() {
            // New Ticket button
            const newTicketBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('+ New Ticket'));
            if (newTicketBtn) {
                newTicketBtn.addEventListener('click', function() {
                    alert('🎫 Create New Support Ticket\n\n✓ Multi-tenant isolation\n✓ Priority assignment\n✓ Auto-routing to agents\n✓ Email notifications\n✓ SLA tracking\n✓ File attachments\n\nThis would open the ticket creation form.');
                });
            }

            // Export Tickets button
            const exportBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Export Tickets'));
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    alert('📊 Export Support Data\n\n✓ CSV/Excel export\n✓ Date range filtering\n✓ Status filtering\n✓ Performance metrics\n✓ SLA compliance reports\n\nGenerating export file...');
                });
            }

            // Ticket cards click handlers
            document.querySelectorAll('.ticket-card').forEach(card => {
                card.addEventListener('click', function() {
                    const title = this.querySelector('h4').textContent;
                    const client = this.querySelector('.text-xs').textContent.split('•')[0].trim();
                    const ticketNumber = this.querySelector('.text-xs').textContent.split('•')[1].trim();
                    const status = this.querySelector('.inline-flex:last-child').textContent;
                    
                    alert(`🎫 Ticket Details\n\nTitle: ${title}\nClient: ${client}\n${ticketNumber}\nStatus: ${status}\n\n✓ View conversation history\n✓ Add internal notes\n✓ Change status/priority\n✓ Assign to agent\n✓ Send client update\n✓ Escalate to manager\n\nThis would open the ticket detail view.`);
                });
            });
        }
    </script>
</body>
</html>
