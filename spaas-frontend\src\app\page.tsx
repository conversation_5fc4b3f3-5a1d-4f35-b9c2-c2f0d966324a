'use client'

import { useRouter } from 'next/navigation'

export default function Home() {
  const router = useRouter()

  const handleSignIn = async (provider: 'google' | 'github' | 'email') => {
    if (provider === 'email') {
      const email = prompt('Enter your email:')
      if (!email) return

      alert(`🚀 SPaaS Platform Demo\n\nIn production, a magic link would be sent to:\n${email}\n\nFor now, click "Dashboard Demo" to see the admin interface!`)
      return
    }

    alert(`🚀 SPaaS Platform Demo\n\n✅ ${provider.toUpperCase()} OAuth Integration Ready\n✅ Supabase Authentication\n✅ Multi-tenant Architecture\n✅ Row-Level Security\n\nIn production, you would be redirected to ${provider} login.\n\nClick "Dashboard Demo" to see the admin interface!`)
  }

  const handleDashboardDemo = () => {
    router.push('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900">SPaaS Platform</h1>
              <span className="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">Beta</span>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => handleSignIn('email')}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Sign In
              </button>
              <button
                onClick={handleDashboardDemo}
                className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
              >
                Dashboard Demo
              </button>
              <button
                onClick={() => handleSignIn('google')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
              >
                Get Started
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            SIP Platform as a Service
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Complete multi-tenant telecommunications platform with AI-powered call flows, 
            real-time analytics, and seamless integrations. Built for resellers and enterprises.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleSignIn('google')}
              className="px-8 py-3 bg-blue-600 text-white rounded-md text-lg font-medium hover:bg-blue-700"
            >
              Start Free Trial
            </button>
            <button
              onClick={handleDashboardDemo}
              className="px-8 py-3 bg-green-600 text-white rounded-md text-lg font-medium hover:bg-green-700"
            >
              Dashboard Demo
            </button>
            <button
              onClick={() => alert('🚀 SPaaS Platform Features:\n\n✅ Multi-tenant architecture\n✅ SIP/VoIP management\n✅ AI-powered IVR\n✅ Real-time analytics\n✅ Enterprise security\n✅ Global integrations\n\nClick "Dashboard Demo" to see the admin interface!')}
              className="px-8 py-3 border border-gray-300 text-gray-700 rounded-md text-lg font-medium hover:bg-gray-50"
            >
              View Features
            </button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Everything you need for modern telephony
          </h2>
          <p className="text-lg text-gray-600">
            Powerful features designed for scalability and ease of use
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
              <div className="w-6 h-6 bg-blue-600 rounded"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Multi-Tenant Architecture</h3>
            <p className="text-gray-600">
              Complete tenant isolation with white-label capabilities and custom branding
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
              <div className="w-6 h-6 bg-green-600 rounded"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">SIP & VoIP Management</h3>
            <p className="text-gray-600">
              Full SIP account management with DID numbers, call routing, and recording
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
              <div className="w-6 h-6 bg-purple-600 rounded"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">AI-Powered IVR</h3>
            <p className="text-gray-600">
              Visual workflow builder with ChatGPT integration for intelligent call flows
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
              <div className="w-6 h-6 bg-orange-600 rounded"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Real-time Analytics</h3>
            <p className="text-gray-600">
              Comprehensive reporting with CDR analysis and usage statistics
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
              <div className="w-6 h-6 bg-red-600 rounded"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Enterprise Security</h3>
            <p className="text-gray-600">
              Row-level security, JWT authentication, and comprehensive audit logging
            </p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
              <div className="w-6 h-6 bg-indigo-600 rounded"></div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Global Integrations</h3>
            <p className="text-gray-600">
              SignalWire, Twilio, Stripe integration with webhook support
            </p>
          </div>
        </div>
      </section>

      {/* Database Schema Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Robust Database Architecture
          </h2>
          <p className="text-lg text-gray-600">
            15 interconnected tables with full multi-tenancy support
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold text-gray-900">Core Tables</h4>
              <ul className="text-sm text-gray-600 mt-2 space-y-1">
                <li>• Tenants (Organizations)</li>
                <li>• Users (Multi-role)</li>
                <li>• Clients (End customers)</li>
              </ul>
            </div>
            <div className="border-l-4 border-green-500 pl-4">
              <h4 className="font-semibold text-gray-900">Telephony</h4>
              <ul className="text-sm text-gray-600 mt-2 space-y-1">
                <li>• SIP Accounts</li>
                <li>• DID Numbers</li>
                <li>• Call Flows (IVR)</li>
                <li>• Call Detail Records</li>
              </ul>
            </div>
            <div className="border-l-4 border-purple-500 pl-4">
              <h4 className="font-semibold text-gray-900">Business</h4>
              <ul className="text-sm text-gray-600 mt-2 space-y-1">
                <li>• Invoices & Billing</li>
                <li>• Support Tickets</li>
                <li>• Audit Logs</li>
                <li>• Webhooks</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-900 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to transform your telecommunications business?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join hundreds of companies already using SPaaS Platform
          </p>
          <button 
            onClick={() => handleSignIn('google')}
            className="px-8 py-3 bg-white text-gray-900 rounded-md text-lg font-medium hover:bg-gray-100"
          >
            Start Your Free Trial
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 SPaaS Platform. Built with Supabase, Next.js, and modern web technologies.</p>
            <p className="mt-2 text-sm">Complete source code available • Multi-tenant • Production-ready</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
