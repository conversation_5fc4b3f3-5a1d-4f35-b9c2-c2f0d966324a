<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Modern Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            background-attachment: fixed;
        }
        
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card-hover {
            transition: all 0.3s ease;
            transform: translateY(0);
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(30px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }
        
        .pulse-glow {
            animation: pulseGlow 2s ease-in-out infinite alternate;
        }
        
        @keyframes pulseGlow {
            from { box-shadow: 0 0 15px rgba(59, 130, 246, 0.3); }
            to { box-shadow: 0 0 25px rgba(59, 130, 246, 0.5); }
        }
        
        .number-animate {
            animation: numberPop 0.6s ease-out;
        }
        
        @keyframes numberPop {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Subtle Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/5 rounded-full blur-xl floating"></div>
        <div class="absolute top-40 right-32 w-24 h-24 bg-blue-300/10 rounded-full blur-xl floating" style="animation-delay: -2s;"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-xl floating" style="animation-delay: -4s;"></div>
    </div>

    <!-- Header -->
    <header class="glass relative z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-white">SPaaS Platform</h1>
                    <span class="ml-3 px-3 py-1 text-xs bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full font-medium">
                        Dashboard
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-white/80">Welcome, <EMAIL></span>
                    <button onclick="goHome()" class="glass px-4 py-2 rounded-lg text-sm font-medium text-white hover:bg-white/20 transition-all">
                        🏠 Back to Home
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="relative z-10 max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="stat-card rounded-2xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white/70">Tenants</p>
                            <p class="text-3xl font-bold text-white number-animate">12</p>
                            <p class="text-xs text-white/60">Active organizations</p>
                        </div>
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="stat-card rounded-2xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white/70">Clients</p>
                            <p class="text-3xl font-bold text-white number-animate">248</p>
                            <p class="text-xs text-white/60">Total customers</p>
                        </div>
                        <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="stat-card rounded-2xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white/70">SIP Accounts</p>
                            <p class="text-3xl font-bold text-white number-animate">1,847</p>
                            <p class="text-xs text-white/60">Active SIP accounts</p>
                        </div>
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="stat-card rounded-2xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white/70">DID Numbers</p>
                            <p class="text-3xl font-bold text-white number-animate">3,421</p>
                            <p class="text-xs text-white/60">Phone numbers</p>
                        </div>
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div class="glass-card rounded-2xl p-6 card-hover cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Manage Tenants</h3>
                            <p class="text-sm text-white/70">View and manage organizations</p>
                        </div>
                    </div>
                </div>

                <div class="glass-card rounded-2xl p-6 card-hover cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Client Management</h3>
                            <p class="text-sm text-white/70">Manage clients and services</p>
                        </div>
                    </div>
                </div>

                <div class="glass-card rounded-2xl p-6 card-hover cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">SIP & Telephony</h3>
                            <p class="text-sm text-white/70">Configure SIP accounts</p>
                        </div>
                    </div>
                </div>

                <div class="glass-card rounded-2xl p-6 card-hover cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">Analytics</h3>
                            <p class="text-sm text-white/70">View reports and statistics</p>
                        </div>
                    </div>
                </div>

                <div class="glass-card rounded-2xl p-6 card-hover cursor-pointer pulse-glow">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">AI Call Flow Builder</h3>
                            <p class="text-sm text-white/70">Design IVR with AI assistance</p>
                        </div>
                    </div>
                </div>

                <div class="glass-card rounded-2xl p-6 card-hover cursor-pointer">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-gradient-to-br from-gray-400 to-gray-600 rounded-2xl flex items-center justify-center mr-4">
                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-white">System Settings</h3>
                            <p class="text-sm text-white/70">Platform configuration</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Architecture -->
            <div class="glass-card rounded-3xl p-8">
                <h3 class="text-2xl font-bold text-white mb-6 text-center">🗄️ Database Architecture</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="border-l-4 border-blue-400 pl-6">
                        <h4 class="text-lg font-bold text-white mb-3">Core Tables (3)</h4>
                        <div class="space-y-2">
                            <div class="text-white/80">• Tenants (Organizations)</div>
                            <div class="text-white/80">• Users (Multi-role)</div>
                            <div class="text-white/80">• Clients (End customers)</div>
                        </div>
                    </div>
                    <div class="border-l-4 border-green-400 pl-6">
                        <h4 class="text-lg font-bold text-white mb-3">Telephony (7)</h4>
                        <div class="space-y-2">
                            <div class="text-white/80">• SIP Accounts</div>
                            <div class="text-white/80">• DID Numbers</div>
                            <div class="text-white/80">• Call Flows (IVR)</div>
                            <div class="text-white/80">• Call Detail Records</div>
                        </div>
                    </div>
                    <div class="border-l-4 border-purple-400 pl-6">
                        <h4 class="text-lg font-bold text-white mb-3">Business (5)</h4>
                        <div class="space-y-2">
                            <div class="text-white/80">• Invoices & Billing</div>
                            <div class="text-white/80">• Support Tickets</div>
                            <div class="text-white/80">• Audit Logs</div>
                            <div class="text-white/80">• Webhooks</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function goHome() {
            window.location.href = 'spaas-modern.html';
        }

        // Add click handlers for demo cards
        document.querySelectorAll('.cursor-pointer').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('h3').textContent;
                alert(`🚀 ${title}\n\nThis would open the ${title.toLowerCase()} interface.\n\nFeatures include:\n• Multi-tenant data isolation\n• Real-time updates\n• Advanced filtering\n• Export capabilities\n• Role-based permissions\n• Modern glassmorphism UI`);
            });
        });

        // Add number animation on load
        window.addEventListener('load', function() {
            const numbers = document.querySelectorAll('.number-animate');
            numbers.forEach((num, index) => {
                setTimeout(() => {
                    num.style.animationDelay = `${index * 0.2}s`;
                }, index * 100);
            });
        });
    </script>
</body>
</html>
