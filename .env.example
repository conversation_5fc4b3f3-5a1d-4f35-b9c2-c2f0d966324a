# SPaaS Platform Environment Variables
# Copy this file to .env and fill in your actual values

# Supabase Configuration
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# OAuth Providers
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

# Twilio/SignalWire Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_MESSAGE_SERVICE_SID=your_twilio_message_service_sid_here
SIGNALWIRE_PROJECT_ID=your_signalwire_project_id_here
SIGNALWIRE_API_TOKEN=your_signalwire_api_token_here
SIGNALWIRE_SPACE_URL=your_signalwire_space_url_here

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# OpenAI Configuration
OPENAI_API_KEY=sk-your_openai_api_key_here

# Email Configuration (SendGrid)
SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>

# AWS S3 Configuration (Optional)
S3_HOST=your_s3_bucket.s3-region.amazonaws.com
S3_REGION=us-east-1
S3_ACCESS_KEY=your_aws_access_key_here
S3_SECRET_KEY=your_aws_secret_key_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Development
NODE_ENV=development
