-- SPaaS Platform Complete RLS Policies
-- Remaining tables and triggers

-- Tickets policies
CREATE POLICY "Providers can access all tickets" ON tickets
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their tickets" ON tickets
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Support staff can manage tickets in their tenant" ON tickets
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'support', 'staff')
  );

CREATE POLICY "Clients can view their own tickets" ON tickets
  FOR SELECT USING (
    get_user_role() = 'client' AND
    client_id IN (
      SELECT id FROM clients
      WHERE tenant_id = get_user_tenant_id()
    )
  );

-- Ticket Messages policies
CREATE POLICY "Providers can access all ticket messages" ON ticket_messages
  FOR ALL USING (is_provider());

CREATE POLICY "Users can access messages for accessible tickets" ON ticket_messages
  FOR SELECT USING (
    ticket_id IN (
      SELECT id FROM tickets
      WHERE can_access_tenant(tenant_id)
    )
  );

CREATE POLICY "Users can create messages for accessible tickets" ON ticket_messages
  FOR INSERT WITH CHECK (
    ticket_id IN (
      SELECT id FROM tickets
      WHERE can_access_tenant(tenant_id)
    )
  );

-- Invoices policies
CREATE POLICY "Providers can access all invoices" ON invoices
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their invoices" ON invoices
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage invoices in their tenant" ON invoices
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );

-- Call Records policies
CREATE POLICY "Providers can access all call records" ON call_records
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their call records" ON call_records
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage call records in their tenant" ON call_records
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );

-- Logs policies
CREATE POLICY "Providers can access all logs" ON logs
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their logs" ON logs
  FOR SELECT USING (can_access_tenant(tenant_id));

-- API Keys policies
CREATE POLICY "Providers can access all api keys" ON api_keys
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their api keys" ON api_keys
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Admins can manage api keys in their tenant" ON api_keys
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin')
  );

-- Pricing Tiers policies
CREATE POLICY "Providers can access all pricing tiers" ON pricing_tiers
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their pricing tiers" ON pricing_tiers
  FOR SELECT USING (
    tenant_id IS NULL OR can_access_tenant(tenant_id)
  );

CREATE POLICY "Admins can manage pricing tiers in their tenant" ON pricing_tiers
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin')
  );

-- Subscriptions policies
CREATE POLICY "Providers can access all subscriptions" ON subscriptions
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their subscriptions" ON subscriptions
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage subscriptions in their tenant" ON subscriptions
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );

-- Prompt Templates policies
CREATE POLICY "Providers can access all prompt templates" ON prompt_templates
  FOR ALL USING (is_provider());

CREATE POLICY "Users can access public prompt templates" ON prompt_templates
  FOR SELECT USING (is_public = true);

CREATE POLICY "Tenant users can access their prompt templates" ON prompt_templates
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage prompt templates in their tenant" ON prompt_templates
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );

-- Create indexes for better performance
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_clients_tenant_id ON clients(tenant_id);
CREATE INDEX idx_sip_accounts_tenant_id ON sip_accounts(tenant_id);
CREATE INDEX idx_sip_accounts_client_id ON sip_accounts(client_id);
CREATE INDEX idx_did_numbers_tenant_id ON did_numbers(tenant_id);
CREATE INDEX idx_did_numbers_client_id ON did_numbers(client_id);
CREATE INDEX idx_call_flows_tenant_id ON call_flows(tenant_id);
CREATE INDEX idx_tickets_tenant_id ON tickets(tenant_id);
CREATE INDEX idx_tickets_client_id ON tickets(client_id);
CREATE INDEX idx_ticket_messages_ticket_id ON ticket_messages(ticket_id);
CREATE INDEX idx_invoices_tenant_id ON invoices(tenant_id);
CREATE INDEX idx_invoices_client_id ON invoices(client_id);
CREATE INDEX idx_call_records_tenant_id ON call_records(tenant_id);
CREATE INDEX idx_call_records_client_id ON call_records(client_id);
CREATE INDEX idx_call_records_start_time ON call_records(start_time);
CREATE INDEX idx_logs_tenant_id ON logs(tenant_id);
CREATE INDEX idx_logs_user_id ON logs(user_id);
CREATE INDEX idx_logs_created_at ON logs(created_at);
CREATE INDEX idx_api_keys_tenant_id ON api_keys(tenant_id);
CREATE INDEX idx_pricing_tiers_tenant_id ON pricing_tiers(tenant_id);
CREATE INDEX idx_subscriptions_tenant_id ON subscriptions(tenant_id);
CREATE INDEX idx_subscriptions_client_id ON subscriptions(client_id);
CREATE INDEX idx_prompt_templates_tenant_id ON prompt_templates(tenant_id);