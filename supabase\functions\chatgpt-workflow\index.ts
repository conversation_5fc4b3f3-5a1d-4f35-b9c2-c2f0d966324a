// ChatGPT Workflow Generator for SPaaS Platform
// AI-powered IVR call flow generation and optimization

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'jsr:@supabase/supabase-js@2'

interface ChatGPTRequest {
  prompt: string
  template_id?: string
  tenant_id: string
  user_id: string
  context?: {
    business_type?: string
    company_name?: string
    requirements?: string
    existing_flow?: any
  }
}

interface ChatGPTResponse {
  success: boolean
  flow_data?: any
  message?: string
  suggestions?: string[]
}

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY') ?? ''

Deno.serve(async (req) => {
  try {
    if (req.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 })
    }

    const { prompt, template_id, tenant_id, user_id, context }: ChatGPTRequest = await req.json()

    if (!prompt || !tenant_id || !user_id) {
      return new Response('Missing required fields', { status: 400 })
    }

    // Check rate limits and permissions
    const canGenerate = await checkRateLimit(tenant_id, user_id)
    if (!canGenerate) {
      return new Response('Rate limit exceeded', { status: 429 })
    }

    // Get prompt template if specified
    let finalPrompt = prompt
    if (template_id) {
      const template = await getPromptTemplate(template_id, tenant_id)
      if (template) {
        finalPrompt = interpolateTemplate(template.prompt_text, context || {})
      }
    }

    // Generate workflow with ChatGPT
    const response = await generateWorkflow(finalPrompt, context)

    // Log the generation
    await logGeneration(tenant_id, user_id, prompt, response)

    return new Response(JSON.stringify(response), {
      headers: { 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('ChatGPT workflow error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
})

async function checkRateLimit(tenantId: string, userId: string): Promise<boolean> {
  // Check if user has exceeded rate limits (e.g., 10 generations per hour)
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()

  const { count } = await supabase
    .from('logs')
    .select('*', { count: 'exact', head: true })
    .eq('tenant_id', tenantId)
    .eq('user_id', userId)
    .eq('action', 'chatgpt_generation')
    .gte('created_at', oneHourAgo)

  return (count || 0) < 10
}

async function getPromptTemplate(templateId: string, tenantId: string) {
  const { data } = await supabase
    .from('prompt_templates')
    .select('*')
    .eq('id', templateId)
    .or(`tenant_id.eq.${tenantId},is_public.eq.true`)
    .single()

  return data
}

function interpolateTemplate(template: string, context: any): string {
  let result = template

  // Replace placeholders like {company_name}, {requirements}, etc.
  Object.keys(context).forEach(key => {
    const placeholder = `{${key}}`
    result = result.replace(new RegExp(placeholder, 'g'), context[key] || '')
  })

  return result
}

async function generateWorkflow(prompt: string, context?: any): Promise<ChatGPTResponse> {
  const systemPrompt = `You are an expert IVR (Interactive Voice Response) system designer for a telecommunications platform.

Your task is to generate call flow configurations in JSON format based on user requirements.

The call flow should use this structure:
{
  "nodes": [
    {
      "id": "unique_node_id",
      "type": "play|gather|dial|record|webhook|branch",
      "data": {
        "message": "Text to speak to caller",
        "number": "Phone number to dial",
        "timeout": 10,
        "url": "Webhook URL",
        "condition": "Condition for branching"
      }
    }
  ],
  "edges": [
    {
      "source": "source_node_id",
      "target": "target_node_id",
      "condition": "dtmf==1"
    }
  ]
}

Node types:
- "play": Speak a message to the caller
- "gather": Collect DTMF input from caller
- "dial": Transfer call to a phone number
- "record": Record caller's message
- "webhook": Make HTTP request to external service
- "branch": Conditional routing based on input

Always start with a node with id "start".
Use professional, clear language for messages.
Include error handling and fallback options.
Make the flow user-friendly and efficient.

Generate ONLY the JSON, no additional text or explanation.`

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 2000
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    const generatedContent = data.choices[0]?.message?.content

    if (!generatedContent) {
      return {
        success: false,
        message: 'No content generated'
      }
    }

    // Try to parse the generated JSON
    try {
      const flowData = JSON.parse(generatedContent)

      // Validate the flow structure
      if (!flowData.nodes || !Array.isArray(flowData.nodes)) {
        throw new Error('Invalid flow structure: missing nodes array')
      }

      if (!flowData.edges || !Array.isArray(flowData.edges)) {
        throw new Error('Invalid flow structure: missing edges array')
      }

      // Check for start node
      const hasStartNode = flowData.nodes.some((node: any) => node.id === 'start')
      if (!hasStartNode) {
        throw new Error('Invalid flow structure: missing start node')
      }

      return {
        success: true,
        flow_data: flowData,
        message: 'Workflow generated successfully'
      }

    } catch (parseError) {
      console.error('Failed to parse generated JSON:', parseError)
      return {
        success: false,
        message: 'Generated content is not valid JSON',
        suggestions: [
          'Try simplifying your requirements',
          'Be more specific about the call flow steps',
          'Check if all required information is provided'
        ]
      }
    }

  } catch (error) {
    console.error('OpenAI API error:', error)
    return {
      success: false,
      message: 'Failed to generate workflow'
    }
  }
}

async function logGeneration(tenantId: string, userId: string, prompt: string, response: ChatGPTResponse) {
  await supabase
    .from('logs')
    .insert({
      tenant_id: tenantId,
      user_id: userId,
      action: 'chatgpt_generation',
      resource_type: 'call_flow',
      details: {
        prompt,
        success: response.success,
        message: response.message
      }
    })
}

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/chatgpt-workflow' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
