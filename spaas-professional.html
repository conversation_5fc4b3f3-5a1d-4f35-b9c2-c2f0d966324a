<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Enterprise Telecommunications Solution</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .bg-corporate {
            background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .card-shadow:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: box-shadow 0.2s ease;
        }
        
        .btn-primary {
            background: #1e40af;
            color: white;
            transition: background-color 0.2s ease;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: white;
            color: #374151;
            border: 1px solid #d1d5db;
            transition: all 0.2s ease;
        }
        
        .btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
    </style>
</head>
<body class="bg-corporate min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h1 class="text-xl font-semibold text-gray-900">SPaaS Platform</h1>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-8">
                    <a href="#features" class="text-gray-600 hover:text-gray-900 font-medium">Features</a>
                    <a href="#pricing" class="text-gray-600 hover:text-gray-900 font-medium">Pricing</a>
                    <a href="#documentation" class="text-gray-600 hover:text-gray-900 font-medium">Documentation</a>
                    <a href="#support" class="text-gray-600 hover:text-gray-900 font-medium">Support</a>
                </nav>
                <div class="flex items-center space-x-4">
                    <button id="signInBtn" class="btn-secondary px-4 py-2 rounded-lg text-sm font-medium">
                        Sign In
                    </button>
                    <button id="dashboardBtn" class="btn-primary px-4 py-2 rounded-lg text-sm font-medium">
                        Dashboard Demo
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                    Enterprise SIP Platform
                    <span class="block text-blue-600">as a Service</span>
                </h1>
                <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                    Complete multi-tenant telecommunications platform with advanced call routing, 
                    real-time analytics, and enterprise-grade security. Built for scalability and reliability.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button id="trialBtn" class="btn-primary px-8 py-4 rounded-lg text-lg font-semibold">
                        Start Free Trial
                    </button>
                    <button id="dashboardBtn2" class="btn-secondary px-8 py-4 rounded-lg text-lg font-semibold">
                        View Dashboard Demo
                    </button>
                    <button id="contactBtn" class="text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:text-blue-700">
                        Contact Sales
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-3xl font-bold text-gray-900">99.9%</div>
                    <div class="text-gray-600">Uptime SLA</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-gray-900">500+</div>
                    <div class="text-gray-600">Enterprise Clients</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-gray-900">50M+</div>
                    <div class="text-gray-600">Calls Processed</div>
                </div>
                <div>
                    <div class="text-3xl font-bold text-gray-900">24/7</div>
                    <div class="text-gray-600">Technical Support</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    Enterprise-Grade Features
                </h2>
                <p class="text-xl text-gray-600">
                    Everything you need for modern telecommunications infrastructure
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg p-8 card-shadow">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Multi-Tenant Architecture</h3>
                    <p class="text-gray-600">
                        Complete tenant isolation with white-label capabilities, custom branding, and independent configurations.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-8 card-shadow">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">SIP & VoIP Management</h3>
                    <p class="text-gray-600">
                        Full SIP account management with DID numbers, call routing, recording, and advanced telephony features.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-8 card-shadow">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Intelligent Call Flows</h3>
                    <p class="text-gray-600">
                        Visual workflow builder with AI integration for creating sophisticated IVR systems and call routing.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-8 card-shadow">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Real-time Analytics</h3>
                    <p class="text-gray-600">
                        Comprehensive reporting with CDR analysis, usage statistics, and business intelligence dashboards.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-8 card-shadow">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Enterprise Security</h3>
                    <p class="text-gray-600">
                        Row-level security, JWT authentication, encryption, and comprehensive audit logging for compliance.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-8 card-shadow">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">Global Integrations</h3>
                    <p class="text-gray-600">
                        SignalWire, Twilio, Stripe integration with webhook support and third-party API connectivity.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Database Architecture -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">
                    Robust Database Architecture
                </h2>
                <p class="text-xl text-gray-600">
                    15 interconnected tables with full multi-tenancy support
                </p>
            </div>

            <div class="bg-gray-50 rounded-lg p-8">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="border-l-4 border-blue-500 pl-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Core Tables</h4>
                        <ul class="text-gray-600 space-y-2">
                            <li>• Tenants (Organizations)</li>
                            <li>• Users (Multi-role)</li>
                            <li>• Clients (End customers)</li>
                        </ul>
                    </div>
                    <div class="border-l-4 border-green-500 pl-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Telephony</h4>
                        <ul class="text-gray-600 space-y-2">
                            <li>• SIP Accounts</li>
                            <li>• DID Numbers</li>
                            <li>• Call Flows (IVR)</li>
                            <li>• Call Detail Records</li>
                        </ul>
                    </div>
                    <div class="border-l-4 border-purple-500 pl-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Business</h4>
                        <ul class="text-gray-600 space-y-2">
                            <li>• Invoices & Billing</li>
                            <li>• Support Tickets</li>
                            <li>• Audit Logs</li>
                            <li>• Webhooks</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 bg-blue-600">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                Ready to modernize your telecommunications infrastructure?
            </h2>
            <p class="text-xl text-blue-100 mb-8">
                Join hundreds of enterprises already using SPaaS Platform
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-white text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-50 transition-colors">
                    Start Free Trial
                </button>
                <button class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    Schedule Demo
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">SPaaS Platform</h3>
                    <p class="text-gray-400">
                        Enterprise telecommunications platform built for scalability and reliability.
                    </p>
                </div>
                <div>
                    <h4 class="text-sm font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Features</a></li>
                        <li><a href="#" class="hover:text-white">Pricing</a></li>
                        <li><a href="#" class="hover:text-white">API</a></li>
                        <li><a href="#" class="hover:text-white">Documentation</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-sm font-semibold mb-4">Company</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">About</a></li>
                        <li><a href="#" class="hover:text-white">Careers</a></li>
                        <li><a href="#" class="hover:text-white">Contact</a></li>
                        <li><a href="#" class="hover:text-white">Support</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-sm font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">Privacy</a></li>
                        <li><a href="#" class="hover:text-white">Terms</a></li>
                        <li><a href="#" class="hover:text-white">Security</a></li>
                        <li><a href="#" class="hover:text-white">Compliance</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 SPaaS Platform. All rights reserved. Built with Supabase and Next.js.</p>
            </div>
        </div>
    </footer>

    <script>
        // Sign In functionality
        document.getElementById('signInBtn').addEventListener('click', function() {
            const email = prompt('Enter your email for authentication:');
            if (email) {
                alert(`SPaaS Platform Authentication\n\nMagic link would be sent to: ${email}\n\n✓ Supabase Authentication\n✓ Enterprise SSO Support\n✓ Multi-factor Authentication\n\nFor demo purposes, click "Dashboard Demo" to continue.`);
            }
        });

        // Dashboard Demo functionality
        function openDashboard() {
            window.location.href = 'spaas-dashboard-professional.html';
        }
        
        document.getElementById('dashboardBtn').addEventListener('click', openDashboard);
        document.getElementById('dashboardBtn2').addEventListener('click', openDashboard);

        // Trial functionality
        document.getElementById('trialBtn').addEventListener('click', function() {
            alert('Start Free Trial\n\n✓ 30-day free trial\n✓ Full platform access\n✓ Technical support\n✓ No credit card required\n\nClick "Dashboard Demo" to explore the interface.');
        });

        // Contact functionality
        document.getElementById('contactBtn').addEventListener('click', function() {
            alert('Contact Sales\n\n✓ Enterprise consultation\n✓ Custom pricing\n✓ Implementation support\n✓ Technical architecture review\n\nEmail: <EMAIL>\nPhone: +****************');
        });

        // CTA buttons
        document.querySelectorAll('section:nth-last-child(2) button').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent.includes('Trial')) {
                    alert('Start Free Trial\n\n✓ 30-day free trial\n✓ Full platform access\n✓ Technical support\n\nClick "Dashboard Demo" to explore!');
                } else {
                    alert('Schedule Demo\n\n✓ Personalized demonstration\n✓ Technical consultation\n✓ Implementation planning\n\nContact: <EMAIL>');
                }
            });
        });
    </script>
</body>
</html>
