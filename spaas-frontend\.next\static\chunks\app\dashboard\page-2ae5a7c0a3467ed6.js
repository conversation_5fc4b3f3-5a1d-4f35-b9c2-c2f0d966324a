(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{3254:(e,s,r)=>{Promise.resolve().then(r.bind(r,7634))},7634:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>C});var t=r(5155),a=r(2115);let n=(0,r(4060).UU)("http://127.0.0.1:54321","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0");var l=r(2085),i=r(2596),c=r(9688);function d(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,c.QP)((0,i.$)(s))}let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),x=a.forwardRef((e,s)=>{let{className:r,variant:a,size:n,asChild:l=!1,...i}=e;return(0,t.jsx)("button",{className:d(o({variant:a,size:n,className:r})),ref:s,...i})});x.displayName="Button";let m=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});m.displayName="Card";let u=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("flex flex-col space-y-1.5 p-6",r),...a})});u.displayName="CardHeader";let h=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:d("text-2xl font-semibold leading-none tracking-tight",r),...a})});h.displayName="CardTitle";let f=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:d("text-sm text-muted-foreground",r),...a})});f.displayName="CardDescription";let g=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("p-6 pt-0",r),...a})});g.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:d("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter";let j=(0,l.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function p(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{className:d(j({variant:r}),s),...a})}var b=r(4835),N=r(8136),v=r(7580),w=r(9420),y=r(2713),A=r(381);function C(){let[e,s]=(0,a.useState)(null),[r,l]=(0,a.useState)(!0),[i,c]=(0,a.useState)({tenants:0,clients:0,sipAccounts:0,didNumbers:0});(0,a.useEffect)(()=>{n.auth.getSession().then(e=>{var r;let{data:{session:t}}=e;s(null!=(r=null==t?void 0:t.user)?r:null),l(!1)});let{data:{subscription:e}}=n.auth.onAuthStateChange((e,r)=>{var t;s(null!=(t=null==r?void 0:r.user)?t:null)});return()=>e.unsubscribe()},[]),(0,a.useEffect)(()=>{e&&d()},[e]);let d=async()=>{try{let[e,s,r,t]=await Promise.all([n.from("tenants").select("id",{count:"exact",head:!0}),n.from("clients").select("id",{count:"exact",head:!0}),n.from("sip_accounts").select("id",{count:"exact",head:!0}),n.from("did_numbers").select("id",{count:"exact",head:!0})]);c({tenants:e.count||0,clients:s.count||0,sipAccounts:r.count||0,didNumbers:t.count||0})}catch(e){console.error("Error loading stats:",e)}},o=async()=>{await n.auth.signOut()};return r?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary"})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"SPaaS Platform"}),(0,t.jsx)(p,{variant:"secondary",className:"ml-2",children:"Demo"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-700",children:["Welcome, ",null==e?void 0:e.email]}),(0,t.jsxs)(x,{variant:"outline",size:"sm",onClick:o,children:[(0,t.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]})]})})}),(0,t.jsx)("main",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsxs)(m,{children:[(0,t.jsxs)(u,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(h,{className:"text-sm font-medium",children:"Tenants"}),(0,t.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(g,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.tenants}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active organizations"})]})]}),(0,t.jsxs)(m,{children:[(0,t.jsxs)(u,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(h,{className:"text-sm font-medium",children:"Clients"}),(0,t.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(g,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.clients}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Total customers"})]})]}),(0,t.jsxs)(m,{children:[(0,t.jsxs)(u,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(h,{className:"text-sm font-medium",children:"SIP Accounts"}),(0,t.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(g,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.sipAccounts}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active SIP accounts"})]})]}),(0,t.jsxs)(m,{children:[(0,t.jsxs)(u,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(h,{className:"text-sm font-medium",children:"DID Numbers"}),(0,t.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(g,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:i.didNumbers}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Phone numbers"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,t.jsx)(m,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,t.jsxs)(u,{children:[(0,t.jsxs)(h,{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-5 w-5 mr-2"}),"Manage Tenants"]}),(0,t.jsx)(f,{children:"View and manage tenant organizations"})]})}),(0,t.jsx)(m,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,t.jsxs)(u,{children:[(0,t.jsxs)(h,{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 mr-2"}),"Client Management"]}),(0,t.jsx)(f,{children:"Manage clients and their services"})]})}),(0,t.jsx)(m,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,t.jsxs)(u,{children:[(0,t.jsxs)(h,{className:"flex items-center",children:[(0,t.jsx)(w.A,{className:"h-5 w-5 mr-2"}),"SIP & Telephony"]}),(0,t.jsx)(f,{children:"Configure SIP accounts and call flows"})]})}),(0,t.jsx)(m,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,t.jsxs)(u,{children:[(0,t.jsxs)(h,{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 mr-2"}),"Analytics"]}),(0,t.jsx)(f,{children:"View call reports and usage statistics"})]})}),(0,t.jsx)(m,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,t.jsxs)(u,{children:[(0,t.jsxs)(h,{className:"flex items-center",children:[(0,t.jsx)(A.A,{className:"h-5 w-5 mr-2"}),"Call Flow Builder"]}),(0,t.jsx)(f,{children:"Design IVR workflows with AI assistance"})]})}),(0,t.jsx)(m,{className:"hover:shadow-md transition-shadow cursor-pointer",children:(0,t.jsxs)(u,{children:[(0,t.jsxs)(h,{className:"flex items-center",children:[(0,t.jsx)(A.A,{className:"h-5 w-5 mr-2"}),"System Settings"]}),(0,t.jsx)(f,{children:"Platform configuration and settings"})]})})]})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[943,441,684,358],()=>s(3254)),_N_E=e.O()}]);