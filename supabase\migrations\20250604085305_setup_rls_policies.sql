-- SPaaS Platform Row-Level Security (RLS) Policies
-- Multi-tenant data isolation and access control

-- Helper function to get current user's tenant_id from JWT
CREATE OR REPLACE FUNCTION get_user_tenant_id()
RETURNS UUID
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT COALESCE(
    (auth.jwt() ->> 'tenant_id')::UUID,
    (SELECT tenant_id FROM users WHERE id = auth.uid())
  );
$$;

-- Helper function to get current user's role from JWT
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT COALESCE(
    auth.jwt() ->> 'role',
    (SELECT role::TEXT FROM users WHERE id = auth.uid())
  );
$$;

-- Helper function to check if user is provider (global admin)
CREATE OR REPLACE FUNCTION is_provider()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT get_user_role() = 'provider';
$$;

-- Helper function to check if user can access tenant data
CREATE OR REPLACE FUNCTION can_access_tenant(tenant_uuid UUID)
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
AS $$
  SELECT
    is_provider() OR
    get_user_tenant_id() = tenant_uuid;
$$;

-- Enable RLS on all tables
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE sip_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE did_numbers ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_flows ENABLE ROW LEVEL SECURITY;
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ticket_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE call_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE pricing_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompt_templates ENABLE ROW LEVEL SECURITY;

-- Tenants policies
CREATE POLICY "Providers can access all tenants" ON tenants
  FOR ALL USING (is_provider());

CREATE POLICY "Users can access their own tenant" ON tenants
  FOR SELECT USING (can_access_tenant(id));

CREATE POLICY "Resellers can update their own tenant" ON tenants
  FOR UPDATE USING (
    get_user_tenant_id() = id AND
    get_user_role() IN ('reseller', 'admin')
  );

-- Users policies
CREATE POLICY "Providers can access all users" ON users
  FOR ALL USING (is_provider());

CREATE POLICY "Users can access users in their tenant" ON users
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Users can update their own profile" ON users
  FOR UPDATE USING (id = auth.uid());

CREATE POLICY "Admins can manage users in their tenant" ON users
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin')
  );

-- Clients policies
CREATE POLICY "Providers can access all clients" ON clients
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their clients" ON clients
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage clients in their tenant" ON clients
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'support', 'sales', 'staff')
  );

-- SIP Accounts policies
CREATE POLICY "Providers can access all sip accounts" ON sip_accounts
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their sip accounts" ON sip_accounts
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage sip accounts in their tenant" ON sip_accounts
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );

CREATE POLICY "Clients can view their own sip accounts" ON sip_accounts
  FOR SELECT USING (
    get_user_role() = 'client' AND
    client_id IN (
      SELECT id FROM clients
      WHERE tenant_id = get_user_tenant_id()
    )
  );

-- DID Numbers policies
CREATE POLICY "Providers can access all did numbers" ON did_numbers
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their did numbers" ON did_numbers
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage did numbers in their tenant" ON did_numbers
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );

-- Call Flows policies
CREATE POLICY "Providers can access all call flows" ON call_flows
  FOR ALL USING (is_provider());

CREATE POLICY "Tenant users can access their call flows" ON call_flows
  FOR SELECT USING (can_access_tenant(tenant_id));

CREATE POLICY "Staff can manage call flows in their tenant" ON call_flows
  FOR ALL USING (
    get_user_tenant_id() = tenant_id AND
    get_user_role() IN ('reseller', 'admin', 'staff')
  );