(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\testing 5\\\\spaas-frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>o});var r=s(7413),a=s(2376),n=s.n(a),l=s(8726),i=s.n(l);s(1135);let o={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,children:e})})}},5275:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},6424:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(5239),a=s(8088),n=s(8170),l=s.n(n),i=s(893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1204)),"C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6798:(e,t,s)=>{Promise.resolve().then(s.bind(s,8964))},6886:(e,t,s)=>{Promise.resolve().then(s.bind(s,1204))},8713:()=>{},8964:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(687),a=s(5773);function n(){let e=(0,a.useRouter)(),t=async e=>{if("email"===e){let e=prompt("Enter your email:");if(!e)return;alert(`🚀 SPaaS Platform Demo

In production, a magic link would be sent to:
${e}

For now, click "Dashboard Demo" to see the admin interface!`);return}alert(`🚀 SPaaS Platform Demo

✅ ${e.toUpperCase()} OAuth Integration Ready
✅ Supabase Authentication
✅ Multi-tenant Architecture
✅ Row-Level Security

In production, you would be redirected to ${e} login.

Click "Dashboard Demo" to see the admin interface!`)},s=()=>{e.push("/dashboard")};return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"SPaaS Platform"}),(0,r.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:"Beta"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:()=>t("email"),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Sign In"}),(0,r.jsx)("button",{onClick:s,className:"px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700",children:"Dashboard Demo"}),(0,r.jsx)("button",{onClick:()=>t("google"),className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700",children:"Get Started"})]})]})})}),(0,r.jsx)("section",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:"SIP Platform as a Service"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Complete multi-tenant telecommunications platform with AI-powered call flows, real-time analytics, and seamless integrations. Built for resellers and enterprises."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("button",{onClick:()=>t("google"),className:"px-8 py-3 bg-blue-600 text-white rounded-md text-lg font-medium hover:bg-blue-700",children:"Start Free Trial"}),(0,r.jsx)("button",{onClick:s,className:"px-8 py-3 bg-green-600 text-white rounded-md text-lg font-medium hover:bg-green-700",children:"Dashboard Demo"}),(0,r.jsx)("button",{onClick:()=>alert('\uD83D\uDE80 SPaaS Platform Features:\n\n✅ Multi-tenant architecture\n✅ SIP/VoIP management\n✅ AI-powered IVR\n✅ Real-time analytics\n✅ Enterprise security\n✅ Global integrations\n\nClick "Dashboard Demo" to see the admin interface!'),className:"px-8 py-3 border border-gray-300 text-gray-700 rounded-md text-lg font-medium hover:bg-gray-50",children:"View Features"})]})]})}),(0,r.jsxs)("section",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Everything you need for modern telephony"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"Powerful features designed for scalability and ease of use"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Multi-Tenant Architecture"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Complete tenant isolation with white-label capabilities and custom branding"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-green-600 rounded"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"SIP & VoIP Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Full SIP account management with DID numbers, call routing, and recording"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-purple-600 rounded"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"AI-Powered IVR"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Visual workflow builder with ChatGPT integration for intelligent call flows"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-orange-600 rounded"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Real-time Analytics"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Comprehensive reporting with CDR analysis and usage statistics"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-red-600 rounded"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Enterprise Security"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Row-level security, JWT authentication, and comprehensive audit logging"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-indigo-600 rounded"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Global Integrations"}),(0,r.jsx)("p",{className:"text-gray-600",children:"SignalWire, Twilio, Stripe integration with webhook support"})]})]})]}),(0,r.jsxs)("section",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Robust Database Architecture"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"15 interconnected tables with full multi-tenancy support"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"Core Tables"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"• Tenants (Organizations)"}),(0,r.jsx)("li",{children:"• Users (Multi-role)"}),(0,r.jsx)("li",{children:"• Clients (End customers)"})]})]}),(0,r.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"Telephony"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"• SIP Accounts"}),(0,r.jsx)("li",{children:"• DID Numbers"}),(0,r.jsx)("li",{children:"• Call Flows (IVR)"}),(0,r.jsx)("li",{children:"• Call Detail Records"})]})]}),(0,r.jsxs)("div",{className:"border-l-4 border-purple-500 pl-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900",children:"Business"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,r.jsx)("li",{children:"• Invoices & Billing"}),(0,r.jsx)("li",{children:"• Support Tickets"}),(0,r.jsx)("li",{children:"• Audit Logs"}),(0,r.jsx)("li",{children:"• Webhooks"})]})]})]})})]}),(0,r.jsx)("section",{className:"bg-gray-900 text-white py-20",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Ready to transform your telecommunications business?"}),(0,r.jsx)("p",{className:"text-xl text-gray-300 mb-8",children:"Join hundreds of companies already using SPaaS Platform"}),(0,r.jsx)("button",{onClick:()=>t("google"),className:"px-8 py-3 bg-white text-gray-900 rounded-md text-lg font-medium hover:bg-gray-100",children:"Start Your Free Trial"})]})}),(0,r.jsx)("footer",{className:"bg-white border-t",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,r.jsxs)("div",{className:"text-center text-gray-600",children:[(0,r.jsx)("p",{children:"\xa9 2024 SPaaS Platform. Built with Supabase, Next.js, and modern web technologies."}),(0,r.jsx)("p",{className:"mt-2 text-sm",children:"Complete source code available • Multi-tenant • Production-ready"})]})})})]})}},8985:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9619:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,145,658],()=>s(6424));module.exports=r})();