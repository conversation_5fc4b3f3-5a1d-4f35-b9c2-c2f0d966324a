<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Modern Design</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            background-attachment: fixed;
        }
        
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .glass-dark {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .card-hover {
            transition: all 0.3s ease;
            transform: translateY(0);
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        
        .btn-glass {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .floating {
            animation: floating 6s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .pulse-glow {
            animation: pulseGlow 3s ease-in-out infinite alternate;
        }

        @keyframes pulseGlow {
            from { box-shadow: 0 0 15px rgba(59, 130, 246, 0.3); }
            to { box-shadow: 0 0 25px rgba(59, 130, 246, 0.5); }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Subtle Background Elements -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-20 w-32 h-32 bg-white/5 rounded-full blur-xl floating"></div>
        <div class="absolute top-40 right-32 w-24 h-24 bg-blue-300/10 rounded-full blur-xl floating" style="animation-delay: -2s;"></div>
        <div class="absolute bottom-32 left-1/4 w-40 h-40 bg-white/5 rounded-full blur-xl floating" style="animation-delay: -4s;"></div>
    </div>

    <!-- Header -->
    <header class="glass relative z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-white">SPaaS Platform</h1>
                    <span class="ml-3 px-3 py-1 text-xs bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full font-medium">
                        Professional
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="signInBtn" class="btn-glass px-4 py-2 rounded-lg text-sm font-medium text-white">
                        Sign In
                    </button>
                    <button id="dashboardBtn" class="btn-gradient px-4 py-2 rounded-lg text-sm font-medium text-white">
                        Dashboard Demo
                    </button>
                    <button id="getStartedBtn" class="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-lg text-sm font-medium text-white border border-white/30 hover:bg-white/30 transition-all">
                        Get Started
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center">
            <h1 class="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
                SIP Platform
                <span class="block gradient-text">as a Service</span>
            </h1>
            <p class="text-xl text-white/80 mb-8 max-w-3xl mx-auto leading-relaxed">
                Complete multi-tenant telecommunications platform with AI-powered call flows, 
                real-time analytics, and seamless integrations. Built for the future.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button id="trialBtn" class="btn-gradient px-8 py-4 rounded-xl text-lg font-semibold text-white pulse-glow">
                    🚀 Start Free Trial
                </button>
                <button id="dashboardBtn2" class="btn-glass px-8 py-4 rounded-xl text-lg font-semibold text-white">
                    📊 Dashboard Demo
                </button>
                <button id="featuresBtn" class="bg-white/10 backdrop-blur-sm px-8 py-4 rounded-xl text-lg font-semibold text-white border border-white/20 hover:bg-white/20 transition-all">
                    ✨ View Features
                </button>
            </div>
        </div>
    </section>

    <!-- Features Grid -->
    <section class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-white mb-4">
                Everything you need for modern telephony
            </h2>
            <p class="text-xl text-white/70">
                Powerful features designed for scalability and ease of use
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="glass rounded-2xl p-8 card-hover">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">Multi-Tenant Architecture</h3>
                <p class="text-white/70 leading-relaxed">
                    Complete tenant isolation with white-label capabilities and custom branding
                </p>
            </div>

            <div class="glass rounded-2xl p-8 card-hover">
                <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">SIP & VoIP Management</h3>
                <p class="text-white/70 leading-relaxed">
                    Full SIP account management with DID numbers, call routing, and recording
                </p>
            </div>

            <div class="glass rounded-2xl p-8 card-hover">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">AI-Powered IVR</h3>
                <p class="text-white/70 leading-relaxed">
                    Visual workflow builder with ChatGPT integration for intelligent call flows
                </p>
            </div>

            <div class="glass rounded-2xl p-8 card-hover">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">Real-time Analytics</h3>
                <p class="text-white/70 leading-relaxed">
                    Comprehensive reporting with CDR analysis and usage statistics
                </p>
            </div>

            <div class="glass rounded-2xl p-8 card-hover">
                <div class="w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">Enterprise Security</h3>
                <p class="text-white/70 leading-relaxed">
                    Row-level security, JWT authentication, and comprehensive audit logging
                </p>
            </div>

            <div class="glass rounded-2xl p-8 card-hover">
                <div class="w-16 h-16 bg-gradient-to-br from-indigo-400 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">Global Integrations</h3>
                <p class="text-white/70 leading-relaxed">
                    SignalWire, Twilio, Stripe integration with webhook support
                </p>
            </div>
        </div>
    </section>

    <!-- Database Schema Section -->
    <section class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-white mb-4">
                Robust Database Architecture
            </h2>
            <p class="text-xl text-white/70">
                15 interconnected tables with full multi-tenancy support
            </p>
        </div>

        <div class="glass rounded-3xl p-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="border-l-4 border-blue-400 pl-6">
                    <h4 class="text-lg font-bold text-white mb-2">Core Tables</h4>
                    <ul class="text-white/70 space-y-1">
                        <li>• Tenants (Organizations)</li>
                        <li>• Users (Multi-role)</li>
                        <li>• Clients (End customers)</li>
                    </ul>
                </div>
                <div class="border-l-4 border-green-400 pl-6">
                    <h4 class="text-lg font-bold text-white mb-2">Telephony</h4>
                    <ul class="text-white/70 space-y-1">
                        <li>• SIP Accounts</li>
                        <li>• DID Numbers</li>
                        <li>• Call Flows (IVR)</li>
                        <li>• Call Detail Records</li>
                    </ul>
                </div>
                <div class="border-l-4 border-purple-400 pl-6">
                    <h4 class="text-lg font-bold text-white mb-2">Business</h4>
                    <ul class="text-white/70 space-y-1">
                        <li>• Invoices & Billing</li>
                        <li>• Support Tickets</li>
                        <li>• Audit Logs</li>
                        <li>• Webhooks</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="relative z-10 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="glass-dark rounded-3xl p-12">
                <h2 class="text-4xl font-bold text-white mb-4">
                    Ready to transform your telecommunications business?
                </h2>
                <p class="text-xl text-white/70 mb-8">
                    Join hundreds of companies already using SPaaS Platform
                </p>
                <button class="btn-gradient px-12 py-4 rounded-xl text-xl font-bold text-white pulse-glow">
                    🚀 Start Your Free Trial
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="relative z-10 glass-dark mt-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center text-white/60">
                <p>&copy; 2024 SPaaS Platform. Built with Supabase, Next.js, and modern web technologies.</p>
                <p class="mt-2 text-sm">Complete source code available • Multi-tenant • Production-ready</p>
            </div>
        </div>
    </footer>

    <script>
        // Sign In functionality
        document.getElementById('signInBtn').addEventListener('click', function() {
            const email = prompt('Enter your email for magic link:');
            if (email) {
                alert(`🚀 SPaaS Platform Demo\n\n✅ Magic link would be sent to:\n${email}\n\n✅ Supabase Authentication Ready\n✅ OAuth Providers Configured\n✅ JWT Token Management\n\nFor demo purposes, click "Dashboard Demo" to continue!`);
            }
        });

        // Dashboard Demo functionality
        function openDashboard() {
            window.location.href = 'spaas-dashboard-modern.html';
        }
        
        document.getElementById('dashboardBtn').addEventListener('click', openDashboard);
        document.getElementById('dashboardBtn2').addEventListener('click', openDashboard);

        // Get Started functionality
        document.getElementById('getStartedBtn').addEventListener('click', function() {
            alert('🚀 SPaaS Platform Demo\n\n✅ Google OAuth Integration Ready\n✅ Multi-tenant Architecture\n✅ Row-Level Security\n✅ Complete Database Schema\n\nIn production, you would be redirected to Google login.\n\nClick "Dashboard Demo" to see the admin interface!');
        });

        document.getElementById('trialBtn').addEventListener('click', function() {
            alert('🚀 Start Your Free Trial\n\n✅ 30-day free trial\n✅ Full platform access\n✅ Multi-tenant setup\n✅ Technical support\n\nClick "Dashboard Demo" to explore the interface!');
        });

        // Features functionality
        document.getElementById('featuresBtn').addEventListener('click', function() {
            alert('🚀 SPaaS Platform Features\n\n✅ Multi-tenant architecture\n✅ SIP/VoIP management\n✅ AI-powered IVR builder\n✅ Real-time analytics\n✅ Enterprise security\n✅ Global integrations\n✅ 15 database tables\n✅ Edge functions\n\nClick "Dashboard Demo" to see it in action!');
        });

        // CTA button
        document.querySelector('section:nth-last-child(2) button').addEventListener('click', function() {
            alert('🚀 Transform Your Business\n\n✅ Complete SPaaS solution\n✅ White-label ready\n✅ Scalable architecture\n✅ Production-ready code\n\nClick "Dashboard Demo" to explore!');
        });
    </script>
</body>
</html>
