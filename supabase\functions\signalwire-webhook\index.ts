// SignalWire Webhook Handler for SPaaS Platform
// Handles incoming call events, CDR processing, and call flow execution

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { createClient } from 'jsr:@supabase/supabase-js@2'

interface CallEvent {
  CallSid: string
  From: string
  To: string
  CallStatus: string
  Direction: string
  Duration?: string
  StartTime?: string
  EndTime?: string
  AnsweredBy?: string
  RecordingUrl?: string
}

interface CallFlowNode {
  id: string
  type: 'play' | 'gather' | 'dial' | 'record' | 'webhook' | 'branch'
  data: {
    message?: string
    number?: string
    timeout?: number
    url?: string
    condition?: string
  }
}

interface CallFlow {
  nodes: CallFlowNode[]
  edges: Array<{
    source: string
    target: string
    condition?: string
  }>
}

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

Deno.serve(async (req) => {
  try {
    const url = new URL(req.url)
    const path = url.pathname

    if (req.method === 'POST') {
      if (path === '/signalwire-webhook/call-status') {
        return await handleCallStatus(req)
      } else if (path === '/signalwire-webhook/call-flow') {
        return await handleCallFlow(req)
      }
    }

    return new Response('Not Found', { status: 404 })
  } catch (error) {
    console.error('Error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
})

async function handleCallStatus(req: Request): Promise<Response> {
  const formData = await req.formData()
  const callEvent: CallEvent = {
    CallSid: formData.get('CallSid') as string,
    From: formData.get('From') as string,
    To: formData.get('To') as string,
    CallStatus: formData.get('CallStatus') as string,
    Direction: formData.get('Direction') as string,
    Duration: formData.get('Duration') as string,
    StartTime: formData.get('StartTime') as string,
    EndTime: formData.get('EndTime') as string,
    AnsweredBy: formData.get('AnsweredBy') as string,
    RecordingUrl: formData.get('RecordingUrl') as string,
  }

  console.log('Call Status Event:', callEvent)

  // Find the DID number to get tenant info
  const { data: didNumber } = await supabase
    .from('did_numbers')
    .select('*, tenant_id, client_id')
    .eq('number', callEvent.To)
    .single()

  if (!didNumber) {
    console.error('DID number not found:', callEvent.To)
    return new Response('DID not found', { status: 404 })
  }

  // Save call record
  const callRecord = {
    call_id: callEvent.CallSid,
    tenant_id: didNumber.tenant_id,
    client_id: didNumber.client_id,
    direction: callEvent.Direction.toLowerCase(),
    caller_number: callEvent.From,
    called_number: callEvent.To,
    status: mapCallStatus(callEvent.CallStatus),
    start_time: callEvent.StartTime ? new Date(callEvent.StartTime).toISOString() : new Date().toISOString(),
    end_time: callEvent.EndTime ? new Date(callEvent.EndTime).toISOString() : null,
    duration_seconds: callEvent.Duration ? parseInt(callEvent.Duration) : 0,
    recording_url: callEvent.RecordingUrl,
    provider: 'signalwire',
    provider_call_id: callEvent.CallSid,
    metadata: callEvent
  }

  const { error } = await supabase
    .from('call_records')
    .upsert(callRecord, { onConflict: 'call_id' })

  if (error) {
    console.error('Error saving call record:', error)
    return new Response('Error saving call record', { status: 500 })
  }

  return new Response('OK', { status: 200 })
}

async function handleCallFlow(req: Request): Promise<Response> {
  const formData = await req.formData()
  const callSid = formData.get('CallSid') as string
  const from = formData.get('From') as string
  const to = formData.get('To') as string
  const digits = formData.get('Digits') as string

  console.log('Call Flow Event:', { callSid, from, to, digits })

  // Find the DID number and its call flow
  const { data: didNumber } = await supabase
    .from('did_numbers')
    .select('*, call_flows(*)')
    .eq('number', to)
    .single()

  if (!didNumber || !didNumber.call_flows) {
    // Default response if no call flow is configured
    const twiml = `<?xml version="1.0" encoding="UTF-8"?>
      <Response>
        <Say>Thank you for calling. Please hold while we connect you.</Say>
        <Dial>${didNumber?.forward_to || '+15551234567'}</Dial>
      </Response>`

    return new Response(twiml, {
      headers: { 'Content-Type': 'application/xml' }
    })
  }

  const callFlow: CallFlow = didNumber.call_flows.flow_data
  const twiml = await executeCallFlow(callFlow, digits)

  return new Response(twiml, {
    headers: { 'Content-Type': 'application/xml' }
  })
}

async function executeCallFlow(callFlow: CallFlow, digits?: string): Promise<string> {
  let currentNode = callFlow.nodes.find(node => node.id === 'start')

  if (!currentNode) {
    return `<?xml version="1.0" encoding="UTF-8"?>
      <Response>
        <Say>Sorry, there was an error processing your call.</Say>
      </Response>`
  }

  // If we have digits, find the next node based on conditions
  if (digits && currentNode) {
    const edge = callFlow.edges.find(edge =>
      edge.source === currentNode!.id &&
      edge.condition &&
      evaluateCondition(edge.condition, digits)
    )

    if (edge) {
      currentNode = callFlow.nodes.find(node => node.id === edge.target)
    }
  }

  if (!currentNode) {
    return `<?xml version="1.0" encoding="UTF-8"?>
      <Response>
        <Say>Invalid option. Please try again.</Say>
      </Response>`
  }

  return generateTwiML(currentNode, callFlow)
}

function evaluateCondition(condition: string, digits: string): boolean {
  // Simple condition evaluation (e.g., "dtmf==1")
  if (condition.includes('dtmf==')) {
    const expectedDigit = condition.split('==')[1]
    return digits === expectedDigit
  }
  return false
}

function generateTwiML(node: CallFlowNode, callFlow: CallFlow): string {
  let twiml = '<?xml version="1.0" encoding="UTF-8"?><Response>'

  switch (node.type) {
    case 'play':
      twiml += `<Say>${node.data.message}</Say>`
      // Add gather if there are outgoing edges
      const hasEdges = callFlow.edges.some(edge => edge.source === node.id)
      if (hasEdges) {
        twiml += `<Gather timeout="${node.data.timeout || 5}" action="/signalwire-webhook/call-flow" method="POST">`
        twiml += `<Say>${node.data.message}</Say>`
        twiml += '</Gather>'
      }
      break

    case 'gather':
      twiml += `<Gather timeout="${node.data.timeout || 10}" action="/signalwire-webhook/call-flow" method="POST">`
      twiml += `<Say>${node.data.message}</Say>`
      twiml += '</Gather>'
      break

    case 'dial':
      twiml += `<Dial>${node.data.number}</Dial>`
      break

    case 'record':
      twiml += `<Record action="/signalwire-webhook/recording" method="POST" />`
      break
  }

  twiml += '</Response>'
  return twiml
}

function mapCallStatus(status: string): string {
  const statusMap: { [key: string]: string } = {
    'completed': 'completed',
    'busy': 'busy',
    'no-answer': 'no_answer',
    'failed': 'failed',
    'canceled': 'cancelled'
  }
  return statusMap[status] || 'failed'
}

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/signalwire-webhook' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"name":"Functions"}'

*/
