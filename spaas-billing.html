<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Billing & Invoices</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        * { font-family: 'Inter', sans-serif; }
        .sidebar-item { transition: all 0.2s ease; }
        .sidebar-item:hover { background-color: #f3f4f6; }
        .sidebar-item.active { background-color: #3b82f6; color: white; }
        .invoice-card { transition: all 0.2s ease; }
        .invoice-card:hover { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">SPaaS</h1>
                </div>
                <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">A</div>
                    <span class="text-sm font-medium text-gray-700">Acme Telecom</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="goHome()" class="text-sm text-gray-600 hover:text-gray-900">← Back to Home</button>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AJ</div>
                        <div class="ml-3 hidden md:block">
                            <div class="text-sm font-medium text-gray-700">Alex Johnson</div>
                            <div class="text-xs text-gray-500"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <nav class="w-64 bg-white border-r border-gray-200 fixed h-full overflow-y-auto">
            <div class="p-4">
                <ul class="space-y-1">
                    <li><a href="spaas-dashboard-professional.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>Dashboard</a></li>
                    <li><a href="spaas-clients.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>Clients</a></li>
                    <li><a href="spaas-sip-accounts.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>SIP Accounts</a></li>
                    <li><a href="spaas-call-flows.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>Call Flows</a></li>
                    <li><a href="#" class="sidebar-item active flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>Billing</a></li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 ml-64 p-8">
            <!-- Page Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Billing & Invoices</h1>
                        <p class="mt-1 text-sm text-gray-500">Manage billing, invoices, and payment processing with Stripe integration</p>
                    </div>
                    <div class="flex space-x-3">
                        <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-50">
                            Export Data
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                            + Create Invoice
                        </button>
                    </div>
                </div>
            </div>

            <!-- Revenue Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Monthly Revenue</p>
                            <p class="text-2xl font-semibold text-gray-900">$24,847</p>
                            <p class="text-xs text-green-600 mt-1">+12.5% from last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Outstanding Invoices</p>
                            <p class="text-2xl font-semibold text-gray-900">$8,420</p>
                            <p class="text-xs text-orange-600 mt-1">7 invoices pending</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Payment Success Rate</p>
                            <p class="text-2xl font-semibold text-gray-900">98.2%</p>
                            <p class="text-xs text-green-600 mt-1">+0.3% from last month</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Average Invoice</p>
                            <p class="text-2xl font-semibold text-gray-900">$1,247</p>
                            <p class="text-xs text-blue-600 mt-1">+8.1% from last month</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                <!-- Recent Invoices -->
                <div class="lg:col-span-2 bg-white rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
                            <button class="text-sm text-blue-600 hover:text-blue-700 font-medium">View All</button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="invoice-card flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center text-white font-medium mr-4">
                                        TC
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">TechCorp Solutions</div>
                                        <div class="text-xs text-gray-500">Invoice #INV-2024-001</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900">$2,450.00</div>
                                    <div class="text-xs text-green-600">Paid</div>
                                </div>
                            </div>

                            <div class="invoice-card flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white font-medium mr-4">
                                        GI
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Global Industries</div>
                                        <div class="text-xs text-gray-500">Invoice #INV-2024-002</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900">$4,890.00</div>
                                    <div class="text-xs text-orange-600">Pending</div>
                                </div>
                            </div>

                            <div class="invoice-card flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center text-white font-medium mr-4">
                                        SS
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">StartupCo</div>
                                        <div class="text-xs text-gray-500">Invoice #INV-2024-003</div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-gray-900">$890.00</div>
                                    <div class="text-xs text-red-600">Overdue</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="bg-white rounded-lg border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Payment Methods</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M1.5 8.67v8.58a3 3 0 003 3h15a3 3 0 003-3V8.67l-8.928 5.493a3 3 0 01-3.144 0L1.5 8.67z"/>
                                            <path d="M22.5 6.908V6.75a3 3 0 00-3-3h-15a3 3 0 00-3 3v.158l9.714 5.978a1.5 1.5 0 001.572 0L22.5 6.908z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Stripe</div>
                                        <div class="text-xs text-gray-500">Credit Cards, ACH</div>
                                    </div>
                                </div>
                                <div class="text-xs text-green-600 font-medium">Active</div>
                            </div>

                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-600 rounded flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">PayPal</div>
                                        <div class="text-xs text-gray-500">PayPal, Venmo</div>
                                    </div>
                                </div>
                                <div class="text-xs text-green-600 font-medium">Active</div>
                            </div>

                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-400 rounded flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M19 14V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zm-5-4c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3zm-9 0c0-2.76 2.24-5 5-5s5 2.24 5 5-2.24 5-5 5-5-2.24-5-5z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Wire Transfer</div>
                                        <div class="text-xs text-gray-500">Bank transfers</div>
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500 font-medium">Available</div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                                Configure Payment Methods
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function goHome() {
            window.location.href = 'spaas-professional.html';
        }

        // Initialize billing functionality
        document.addEventListener('DOMContentLoaded', function() {
            setupBillingHandlers();
        });

        function setupBillingHandlers() {
            // Create Invoice button
            const createInvoiceBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('+ Create Invoice'));
            if (createInvoiceBtn) {
                createInvoiceBtn.addEventListener('click', function() {
                    alert('💰 Create New Invoice\n\n✓ Stripe integration\n✓ Automated billing cycles\n✓ Multi-currency support\n✓ PDF generation\n✓ Email notifications\n\nThis would open the invoice creation wizard.');
                });
            }

            // Export Data button
            const exportBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Export Data'));
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    alert('📊 Export Billing Data\n\n✓ CSV/Excel export\n✓ Date range selection\n✓ Client filtering\n✓ Revenue reports\n✓ Tax compliance\n\nGenerating export file...');
                });
            }

            // Configure Payment Methods button
            const configureBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Configure Payment Methods'));
            if (configureBtn) {
                configureBtn.addEventListener('click', function() {
                    alert('⚙️ Payment Method Configuration\n\n🔵 Stripe Setup:\n✓ API keys configuration\n✓ Webhook endpoints\n✓ Payment processing\n\n🟣 PayPal Integration:\n✓ Business account linking\n✓ IPN configuration\n\n🏦 Wire Transfer:\n✓ Bank account details\n✓ Manual processing');
                });
            }

            // Invoice cards click handlers
            document.querySelectorAll('.invoice-card').forEach(card => {
                card.addEventListener('click', function() {
                    const invoiceNumber = this.querySelector('.text-xs').textContent;
                    const clientName = this.querySelector('.text-sm.font-medium').textContent;
                    const amount = this.querySelector('.text-sm.font-semibold').textContent;
                    const status = this.querySelector('.text-xs:last-child').textContent;

                    alert(`📄 Invoice Details\n\nClient: ${clientName}\nInvoice: ${invoiceNumber}\nAmount: ${amount}\nStatus: ${status}\n\n✓ View PDF\n✓ Send reminder\n✓ Process payment\n✓ Edit invoice\n\nThis would open the invoice detail view.`);
                });
            });

            // View All button
            const viewAllBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('View All'));
            if (viewAllBtn) {
                viewAllBtn.addEventListener('click', function() {
                    alert('📋 All Invoices\n\n✓ Complete invoice history\n✓ Advanced filtering\n✓ Bulk operations\n✓ Payment tracking\n✓ Revenue analytics\n\nThis would open the full invoices page.');
                });
            }
        }
    </script>
</body>
</html>