-- SPaaS Platform Seed Data
-- Test data for development and demo

-- Insert demo tenants
INSERT INTO tenants (id, name, slug, domain, status, billing_email, phone, timezone) VALUES
('550e8400-e29b-41d4-a716-************', 'Acme Telecom', 'acme-telecom', 'acme-telecom.com', 'active', '<EMAIL>', '******-0101', 'America/New_York'),
('550e8400-e29b-41d4-a716-************', 'Global Voice Solutions', 'global-voice', 'globalvoice.com', 'active', '<EMAIL>', '******-0102', 'America/Los_Angeles'),
('550e8400-e29b-41d4-a716-************', 'TechCorp Communications', 'techcorp', 'techcorp.net', 'trial', '<EMAIL>', '******-0103', 'UTC');

-- Insert demo pricing tiers
INSERT INTO pricing_tiers (id, tenant_id, name, description, monthly_fee, setup_fee, per_minute_rate, included_minutes, max_concurrent_calls, features) VALUES
('660e8400-e29b-41d4-a716-************', NULL, 'Basic Plan', 'Basic SIP service for small businesses', 29.99, 0, 0.02, 1000, 2, '{"sip_accounts": 5, "did_numbers": 1, "call_recording": false}'),
('660e8400-e29b-41d4-a716-************', NULL, 'Professional Plan', 'Advanced features for growing businesses', 79.99, 25.00, 0.015, 3000, 5, '{"sip_accounts": 25, "did_numbers": 5, "call_recording": true, "ivr": true}'),
('660e8400-e29b-41d4-a716-************', NULL, 'Enterprise Plan', 'Full-featured solution for large organizations', 199.99, 50.00, 0.01, 10000, 20, '{"sip_accounts": 100, "did_numbers": 25, "call_recording": true, "ivr": true, "api_access": true}');

-- Insert demo clients for Acme Telecom
INSERT INTO clients (id, tenant_id, name, email, phone, company, contact_person, created_by) VALUES
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Smith & Associates', '<EMAIL>', '******-1001', 'Smith & Associates Law Firm', 'John Smith', NULL),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Downtown Dental', '<EMAIL>', '******-1002', 'Downtown Dental Clinic', 'Dr. Sarah Johnson', NULL),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Metro Pizza', '<EMAIL>', '******-1003', 'Metro Pizza Restaurant', 'Mike Rodriguez', NULL);

-- Insert demo clients for Global Voice Solutions
INSERT INTO clients (id, tenant_id, name, email, phone, company, contact_person, created_by) VALUES
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'TechStart Inc', '<EMAIL>', '******-2001', 'TechStart Startup', 'Alex Chen', NULL),
('770e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Green Energy Co', '<EMAIL>', '******-2002', 'Green Energy Solutions', 'Emma Wilson', NULL);

-- Insert demo SIP accounts
INSERT INTO sip_accounts (id, tenant_id, client_id, username, password, domain, display_name, status, max_concurrent_calls) VALUES
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'smith001', 'SecurePass123!', 'sip.acme-telecom.com', 'Smith & Associates Main', 'active', 3),
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'dental001', 'DentalSip456!', 'sip.acme-telecom.com', 'Downtown Dental Reception', 'active', 2),
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'pizza001', 'PizzaOrder789!', 'sip.acme-telecom.com', 'Metro Pizza Orders', 'active', 5),
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'techstart001', 'TechSip321!', 'sip.globalvoice.com', 'TechStart Main Line', 'active', 2),
('880e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'green001', 'GreenVoice654!', 'sip.globalvoice.com', 'Green Energy Support', 'active', 3);

-- Insert demo DID numbers
INSERT INTO did_numbers (id, tenant_id, client_id, number, country_code, area_code, provider, monthly_cost, setup_cost) VALUES
('990e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '+15551234567', '+1', '555', 'signalwire', 2.99, 0),
('990e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '+15551234568', '+1', '555', 'signalwire', 2.99, 0),
('990e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '+15551234569', '+1', '555', 'signalwire', 2.99, 0),
('990e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '+15552345678', '+1', '555', 'signalwire', 2.99, 0),
('990e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '+15552345679', '+1', '555', 'signalwire', 2.99, 0);

-- Insert demo call flows (IVR scenarios)
INSERT INTO call_flows (id, tenant_id, name, description, flow_data, created_by) VALUES
('aa0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Basic Reception IVR', 'Simple reception menu with options',
'{"nodes": [{"id": "start", "type": "play", "data": {"message": "Thank you for calling. Press 1 for sales, 2 for support, or 0 for operator."}}, {"id": "sales", "type": "dial", "data": {"number": "ext101"}}, {"id": "support", "type": "dial", "data": {"number": "ext102"}}, {"id": "operator", "type": "dial", "data": {"number": "ext100"}}], "edges": [{"source": "start", "target": "sales", "condition": "dtmf==1"}, {"source": "start", "target": "support", "condition": "dtmf==2"}, {"source": "start", "target": "operator", "condition": "dtmf==0"}]}', NULL),

('aa0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Pizza Order System', 'Automated pizza ordering system',
'{"nodes": [{"id": "start", "type": "play", "data": {"message": "Welcome to Metro Pizza! Press 1 to place an order, 2 for store hours, or 3 to speak with someone."}}, {"id": "order", "type": "gather", "data": {"message": "Please enter your phone number followed by the pound key.", "timeout": 10}}, {"id": "hours", "type": "play", "data": {"message": "We are open Monday through Sunday, 11 AM to 11 PM. Thank you for calling!"}}, {"id": "human", "type": "dial", "data": {"number": "ext200"}}], "edges": [{"source": "start", "target": "order", "condition": "dtmf==1"}, {"source": "start", "target": "hours", "condition": "dtmf==2"}, {"source": "start", "target": "human", "condition": "dtmf==3"}]}', NULL);

-- Insert demo prompt templates for ChatGPT integration
INSERT INTO prompt_templates (id, tenant_id, name, description, prompt_text, category, is_public) VALUES
('bb0e8400-e29b-41d4-a716-************', NULL, 'Basic IVR Generator', 'Generate a simple IVR menu', 'Create an IVR call flow with the following requirements: {requirements}. Include play prompts, gather input, and dial actions. Return the flow as JSON with nodes and edges.', 'ivr', true),
('bb0e8400-e29b-41d4-a716-************', NULL, 'Business Hours IVR', 'Generate business hours announcement', 'Create an IVR that announces business hours for {business_type}. Hours are {hours}. Include options to leave a message or transfer to emergency line.', 'ivr', true),
('bb0e8400-e29b-41d4-a716-************', NULL, 'Customer Service Menu', 'Generate customer service menu', 'Create a customer service IVR menu for {company_name}. Include options for: {options}. Make it professional and user-friendly.', 'ivr', true);

-- Insert demo subscriptions
INSERT INTO subscriptions (id, tenant_id, client_id, pricing_tier_id, status, current_period_start, current_period_end) VALUES
('cc0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'active', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 month'),
('cc0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'active', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 month'),
('cc0e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '660e8400-e29b-41d4-a716-************', 'active', CURRENT_DATE, CURRENT_DATE + INTERVAL '1 month');
