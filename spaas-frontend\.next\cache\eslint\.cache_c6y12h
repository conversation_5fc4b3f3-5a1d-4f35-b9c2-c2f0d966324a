[{"C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\dashboard\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\badge.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\button.tsx": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\card.tsx": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\input.tsx": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\lib\\supabase.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\lib\\utils.ts": "9"}, {"size": 8295, "mtime": 1749032240514, "results": "10", "hashOfConfig": "11"}, {"size": 689, "mtime": 1749030984706, "results": "12", "hashOfConfig": "11"}, {"size": 10570, "mtime": 1749033983583, "results": "13", "hashOfConfig": "11"}, {"size": 1412, "mtime": 1749031718333, "results": "14", "hashOfConfig": "11"}, {"size": 1749, "mtime": 1749031626566, "results": "15", "hashOfConfig": "11"}, {"size": 1876, "mtime": 1749031637942, "results": "16", "hashOfConfig": "11"}, {"size": 823, "mtime": 1749031657446, "results": "17", "hashOfConfig": "11"}, {"size": 8510, "mtime": 1749031511647, "results": "18", "hashOfConfig": "11"}, {"size": 3216, "mtime": 1749031529019, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "15ulfpp", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\dashboard\\page.tsx", ["47"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\button.tsx", ["48"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\components\\ui\\input.tsx", ["49"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\lib\\supabase.ts", ["50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\testing 5\\spaas-frontend\\src\\lib\\utils.ts", ["65", "66"], [], {"ruleId": "67", "severity": 2, "message": "68", "line": 11, "column": 36, "nodeType": "69", "messageId": "70", "endLine": 11, "endColumn": 39, "suggestions": "71"}, {"ruleId": "72", "severity": 2, "message": "73", "line": 41, "column": 32, "nodeType": null, "messageId": "74", "endLine": 41, "endColumn": 39}, {"ruleId": "75", "severity": 2, "message": "76", "line": 4, "column": 18, "nodeType": "77", "messageId": "78", "endLine": 4, "endColumn": 28, "suggestions": "79"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 20, "column": 21, "nodeType": "69", "messageId": "70", "endLine": 20, "endColumn": 24, "suggestions": "80"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 35, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 35, "endColumn": 25, "suggestions": "81"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 50, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 50, "endColumn": 25, "suggestions": "82"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 70, "column": 21, "nodeType": "69", "messageId": "70", "endLine": 70, "endColumn": 24, "suggestions": "83"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 84, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 84, "endColumn": 25, "suggestions": "84"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 98, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 98, "endColumn": 25, "suggestions": "85"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 165, "column": 21, "nodeType": "69", "messageId": "70", "endLine": 165, "endColumn": 24, "suggestions": "86"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 180, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 180, "endColumn": 25, "suggestions": "87"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 195, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 195, "endColumn": 25, "suggestions": "88"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 215, "column": 21, "nodeType": "69", "messageId": "70", "endLine": 215, "endColumn": 24, "suggestions": "89"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 233, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 233, "endColumn": 25, "suggestions": "90"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 251, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 251, "endColumn": 25, "suggestions": "91"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 262, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 262, "endColumn": 25, "suggestions": "92"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 274, "column": 22, "nodeType": "69", "messageId": "70", "endLine": 274, "endColumn": 25, "suggestions": "93"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 286, "column": 23, "nodeType": "69", "messageId": "70", "endLine": 286, "endColumn": 26, "suggestions": "94"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 107, "column": 46, "nodeType": "69", "messageId": "70", "endLine": 107, "endColumn": 49, "suggestions": "95"}, {"ruleId": "67", "severity": 2, "message": "68", "line": 107, "column": 56, "nodeType": "69", "messageId": "70", "endLine": 107, "endColumn": 59, "suggestions": "96"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["97", "98"], "@typescript-eslint/no-unused-vars", "'as<PERSON><PERSON>d' is assigned a value but never used.", "unusedVar", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["99"], ["100", "101"], ["102", "103"], ["104", "105"], ["106", "107"], ["108", "109"], ["110", "111"], ["112", "113"], ["114", "115"], ["116", "117"], ["118", "119"], ["120", "121"], ["122", "123"], ["124", "125"], ["126", "127"], ["128", "129"], ["130", "131"], ["132", "133"], {"messageId": "134", "fix": "135", "desc": "136"}, {"messageId": "137", "fix": "138", "desc": "139"}, {"messageId": "140", "fix": "141", "desc": "142"}, {"messageId": "134", "fix": "143", "desc": "136"}, {"messageId": "137", "fix": "144", "desc": "139"}, {"messageId": "134", "fix": "145", "desc": "136"}, {"messageId": "137", "fix": "146", "desc": "139"}, {"messageId": "134", "fix": "147", "desc": "136"}, {"messageId": "137", "fix": "148", "desc": "139"}, {"messageId": "134", "fix": "149", "desc": "136"}, {"messageId": "137", "fix": "150", "desc": "139"}, {"messageId": "134", "fix": "151", "desc": "136"}, {"messageId": "137", "fix": "152", "desc": "139"}, {"messageId": "134", "fix": "153", "desc": "136"}, {"messageId": "137", "fix": "154", "desc": "139"}, {"messageId": "134", "fix": "155", "desc": "136"}, {"messageId": "137", "fix": "156", "desc": "139"}, {"messageId": "134", "fix": "157", "desc": "136"}, {"messageId": "137", "fix": "158", "desc": "139"}, {"messageId": "134", "fix": "159", "desc": "136"}, {"messageId": "137", "fix": "160", "desc": "139"}, {"messageId": "134", "fix": "161", "desc": "136"}, {"messageId": "137", "fix": "162", "desc": "139"}, {"messageId": "134", "fix": "163", "desc": "136"}, {"messageId": "137", "fix": "164", "desc": "139"}, {"messageId": "134", "fix": "165", "desc": "136"}, {"messageId": "137", "fix": "166", "desc": "139"}, {"messageId": "134", "fix": "167", "desc": "136"}, {"messageId": "137", "fix": "168", "desc": "139"}, {"messageId": "134", "fix": "169", "desc": "136"}, {"messageId": "137", "fix": "170", "desc": "139"}, {"messageId": "134", "fix": "171", "desc": "136"}, {"messageId": "137", "fix": "172", "desc": "139"}, {"messageId": "134", "fix": "173", "desc": "136"}, {"messageId": "137", "fix": "174", "desc": "139"}, {"messageId": "134", "fix": "175", "desc": "136"}, {"messageId": "137", "fix": "176", "desc": "139"}, "suggestUnknown", {"range": "177", "text": "178"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "179", "text": "180"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceEmptyInterfaceWithSuper", {"range": "181", "text": "182"}, "Replace empty interface with a type alias.", {"range": "183", "text": "178"}, {"range": "184", "text": "180"}, {"range": "185", "text": "178"}, {"range": "186", "text": "180"}, {"range": "187", "text": "178"}, {"range": "188", "text": "180"}, {"range": "189", "text": "178"}, {"range": "190", "text": "180"}, {"range": "191", "text": "178"}, {"range": "192", "text": "180"}, {"range": "193", "text": "178"}, {"range": "194", "text": "180"}, {"range": "195", "text": "178"}, {"range": "196", "text": "180"}, {"range": "197", "text": "178"}, {"range": "198", "text": "180"}, {"range": "199", "text": "178"}, {"range": "200", "text": "180"}, {"range": "201", "text": "178"}, {"range": "202", "text": "180"}, {"range": "203", "text": "178"}, {"range": "204", "text": "180"}, {"range": "205", "text": "178"}, {"range": "206", "text": "180"}, {"range": "207", "text": "178"}, {"range": "208", "text": "180"}, {"range": "209", "text": "178"}, {"range": "210", "text": "180"}, {"range": "211", "text": "178"}, {"range": "212", "text": "180"}, {"range": "213", "text": "178"}, {"range": "214", "text": "180"}, {"range": "215", "text": "178"}, {"range": "216", "text": "180"}, [448, 451], "unknown", [448, 451], "never", [72, 149], "type InputProps = React.InputHTMLAttributes<HTMLInputElement>", [570, 573], [570, 573], [1013, 1016], [1013, 1016], [1464, 1467], [1464, 1467], [2073, 2076], [2073, 2076], [2534, 2537], [2534, 2537], [2999, 3002], [2999, 3002], [4913, 4916], [4913, 4916], [5367, 5370], [5367, 5370], [5828, 5831], [5828, 5831], [6375, 6378], [6375, 6378], [6906, 6909], [6906, 6909], [7441, 7444], [7441, 7444], [7688, 7691], [7688, 7691], [8001, 8004], [8001, 8004], [8322, 8325], [8322, 8325], [2983, 2986], [2983, 2986], [2993, 2996], [2993, 2996]]