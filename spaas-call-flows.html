<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Call Flow Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        * { font-family: 'Inter', sans-serif; }
        .sidebar-item { transition: all 0.2s ease; }
        .sidebar-item:hover { background-color: #f3f4f6; }
        .sidebar-item.active { background-color: #3b82f6; color: white; }
        .flow-node { 
            border: 2px solid #e5e7eb; 
            border-radius: 8px; 
            padding: 12px; 
            margin: 8px; 
            background: white;
            cursor: move;
            transition: all 0.2s ease;
        }
        .flow-node:hover { border-color: #3b82f6; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .canvas { 
            background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
            background-size: 20px 20px;
            min-height: 500px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">SPaaS</h1>
                </div>
                <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">A</div>
                    <span class="text-sm font-medium text-gray-700">Acme Telecom</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="goHome()" class="text-sm text-gray-600 hover:text-gray-900">← Back to Home</button>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AJ</div>
                        <div class="ml-3 hidden md:block">
                            <div class="text-sm font-medium text-gray-700">Alex Johnson</div>
                            <div class="text-xs text-gray-500"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <nav class="w-64 bg-white border-r border-gray-200 fixed h-full overflow-y-auto">
            <div class="p-4">
                <ul class="space-y-1">
                    <li><a href="spaas-dashboard-professional.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>Dashboard</a></li>
                    <li><a href="spaas-clients.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>Clients</a></li>
                    <li><a href="spaas-sip-accounts.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>SIP Accounts</a></li>
                    <li><a href="#" class="sidebar-item active flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>Call Flows</a></li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 ml-64">
            <!-- Page Header -->
            <div class="p-6 border-b border-gray-200 bg-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Call Flow Builder</h1>
                        <p class="mt-1 text-sm text-gray-500">Design intelligent IVR workflows with AI assistance</p>
                    </div>
                    <div class="flex space-x-3">
                        <button id="aiAssistBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            AI Assistant
                        </button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200">
                            Preview
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                            Save Flow
                        </button>
                    </div>
                </div>
            </div>

            <div class="flex h-screen">
                <!-- Toolbox -->
                <div class="w-64 bg-white border-r border-gray-200 p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-4">Flow Components</h3>
                    
                    <!-- Basic Components -->
                    <div class="mb-6">
                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Basic</h4>
                        <div class="space-y-2">
                            <div class="flow-node bg-green-50 border-green-200" draggable="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-5-9a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Start</div>
                                        <div class="text-xs text-gray-500">Entry point</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flow-node bg-blue-50 border-blue-200" draggable="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Play</div>
                                        <div class="text-xs text-gray-500">Audio message</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flow-node bg-purple-50 border-purple-200" draggable="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Gather</div>
                                        <div class="text-xs text-gray-500">Collect input</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Components -->
                    <div class="mb-6">
                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Advanced</h4>
                        <div class="space-y-2">
                            <div class="flow-node bg-orange-50 border-orange-200" draggable="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Dial</div>
                                        <div class="text-xs text-gray-500">Make call</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flow-node bg-indigo-50 border-indigo-200" draggable="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Branch</div>
                                        <div class="text-xs text-gray-500">Conditional</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flow-node bg-red-50 border-red-200" draggable="true">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">Webhook</div>
                                        <div class="text-xs text-gray-500">API call</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Templates -->
                    <div>
                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">AI Templates</h4>
                        <div class="space-y-2">
                            <button class="w-full text-left p-3 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg hover:from-purple-100 hover:to-pink-100">
                                <div class="text-sm font-medium text-gray-900">Customer Service</div>
                                <div class="text-xs text-gray-500">AI-powered support flow</div>
                            </button>
                            <button class="w-full text-left p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg hover:from-blue-100 hover:to-indigo-100">
                                <div class="text-sm font-medium text-gray-900">Sales Routing</div>
                                <div class="text-xs text-gray-500">Intelligent lead routing</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Canvas Area -->
                <div class="flex-1 canvas bg-gray-50 relative">
                    <div class="absolute inset-0 p-8">
                        <div class="bg-white rounded-lg border-2 border-dashed border-gray-300 h-full flex items-center justify-center">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Design Your Call Flow</h3>
                                <p class="mt-1 text-sm text-gray-500">Drag components from the toolbox or use AI Assistant to generate flows</p>
                                <div class="mt-6">
                                    <button id="aiGenerateBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700">
                                        🤖 Generate with AI
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Properties Panel -->
                <div class="w-80 bg-white border-l border-gray-200 p-4">
                    <h3 class="text-sm font-semibold text-gray-900 mb-4">Properties</h3>
                    <div class="text-sm text-gray-500">
                        Select a component to edit its properties
                    </div>
                    
                    <!-- AI Chat Panel -->
                    <div class="mt-8">
                        <h4 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            AI Assistant
                        </h4>
                        <div class="bg-gray-50 rounded-lg p-4 mb-4 h-64 overflow-y-auto" id="aiChat">
                            <div class="text-sm text-gray-600">
                                👋 Hi! I'm your AI assistant. I can help you create call flows using natural language.
                                <br><br>
                                Try saying: "Create an IVR that plays a welcome message, then if they press 1 route to sales, if they press 2 route to support"
                            </div>
                        </div>
                        <div class="flex">
                            <input type="text" id="aiInput" placeholder="Describe your call flow..." class="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <button id="aiSendBtn" class="bg-purple-600 text-white px-4 py-2 rounded-r-lg text-sm font-medium hover:bg-purple-700">
                                Send
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function goHome() {
            window.location.href = 'spaas-professional.html';
        }

        // AI Assistant functionality
        document.getElementById('aiAssistBtn').addEventListener('click', function() {
            alert('AI Assistant - Call Flow Builder\n\n🤖 ChatGPT Integration\n✓ Natural language processing\n✓ Auto-generate IVR flows\n✓ Smart suggestions\n✓ Real-time assistance\n\nExample: "Create a customer service flow with menu options"\n\nThis would open the AI chat interface.');
        });

        document.getElementById('aiGenerateBtn').addEventListener('click', function() {
            alert('AI Flow Generation\n\n🤖 Describe your call flow in plain English:\n\n"Create an IVR that:\n- Plays welcome message\n- Press 1 for Sales\n- Press 2 for Support\n- Press 0 for Operator"\n\nAI will generate the complete flow automatically!');
        });

        document.getElementById('aiSendBtn').addEventListener('click', function() {
            const input = document.getElementById('aiInput');
            const chat = document.getElementById('aiChat');
            
            if (input.value.trim()) {
                // Add user message
                chat.innerHTML += `<div class="mb-3"><div class="text-sm font-medium text-gray-900">You:</div><div class="text-sm text-gray-600">${input.value}</div></div>`;
                
                // Simulate AI response
                setTimeout(() => {
                    chat.innerHTML += `<div class="mb-3"><div class="text-sm font-medium text-purple-600">AI Assistant:</div><div class="text-sm text-gray-600">I'll create that call flow for you! Here's what I understand:<br><br>✓ Welcome message<br>✓ Menu options<br>✓ Routing logic<br><br>Generating flow components now...</div></div>`;
                    chat.scrollTop = chat.scrollHeight;
                }, 1000);
                
                input.value = '';
                chat.scrollTop = chat.scrollHeight;
            }
        });

        // Enter key support for AI chat
        document.getElementById('aiInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('aiSendBtn').click();
            }
        });

        // Template buttons
        document.querySelectorAll('button[class*="gradient"]').forEach(btn => {
            btn.addEventListener('click', function() {
                const template = this.querySelector('.text-sm').textContent;
                alert(`AI Template: ${template}\n\n🤖 This would load a pre-built AI template with:\n✓ Optimized flow structure\n✓ Best practices\n✓ Industry standards\n✓ Customizable components\n\nReady to deploy or modify as needed.`);
            });
        });

        // Save and Preview buttons
        document.addEventListener('DOMContentLoaded', function() {
            const saveBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Save Flow'));
            const previewBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Preview'));

            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    alert('Save Call Flow\n\n✓ JSON structure saved\n✓ Version control\n✓ Multi-tenant isolation\n✓ SignalWire/Twilio compatible\n✓ Edge Function deployment\n\nFlow saved successfully!');
                });
            }

            if (previewBtn) {
                previewBtn.addEventListener('click', function() {
                    alert('Preview Call Flow\n\n✓ Interactive simulation\n✓ Test call routing\n✓ Validate logic\n✓ Audio preview\n✓ Performance metrics\n\nThis would open the flow preview window.');
                });
            }
        });
    </script>
</body>
</html>
