<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SPaaS Platform - Call Flow Builder</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        * { font-family: 'Inter', sans-serif; }
        .sidebar-item { transition: all 0.2s ease; }
        .sidebar-item:hover { background-color: #f3f4f6; }
        .sidebar-item.active { background-color: #3b82f6; color: white; }

        /* SignalWire-style nodes */
        .sw-node {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            margin: 8px;
            min-width: 180px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .sw-node:hover {
            border-color: #3b82f6;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
            transform: translateY(-2px);
        }
        .sw-node.selected {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Node connectors */
        .node-connector {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #64748b;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            cursor: crosshair;
        }
        .node-connector.input { left: -6px; }
        .node-connector.output { right: -6px; }
        .node-connector:hover { background: #3b82f6; transform: translateY(-50%) scale(1.2); }

        /* Canvas */
        .flow-canvas {
            background-color: #f8fafc;
            background-image:
                linear-gradient(#e2e8f0 1px, transparent 1px),
                linear-gradient(90deg, #e2e8f0 1px, transparent 1px);
            background-size: 20px 20px;
            min-height: 600px;
            position: relative;
        }

        /* Node categories */
        .node-category {
            margin-bottom: 24px;
        }
        .category-title {
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #64748b;
            margin-bottom: 8px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 fixed w-full top-0 z-50">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">SPaaS</h1>
                </div>
                <div class="flex items-center bg-blue-50 px-3 py-1 rounded-full">
                    <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium mr-2">A</div>
                    <span class="text-sm font-medium text-gray-700">Acme Telecom</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="goHome()" class="text-sm text-gray-600 hover:text-gray-900">← Back to Home</button>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white text-sm font-medium">AJ</div>
                        <div class="ml-3 hidden md:block">
                            <div class="text-sm font-medium text-gray-700">Alex Johnson</div>
                            <div class="text-xs text-gray-500"><EMAIL></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex pt-16">
        <!-- Sidebar -->
        <nav class="w-64 bg-white border-r border-gray-200 fixed h-full overflow-y-auto">
            <div class="p-4">
                <ul class="space-y-1">
                    <li><a href="spaas-dashboard-professional.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path></svg>Dashboard</a></li>
                    <li><a href="spaas-clients.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>Clients</a></li>
                    <li><a href="spaas-sip-accounts.html" class="sidebar-item flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>SIP Accounts</a></li>
                    <li><a href="#" class="sidebar-item active flex items-center px-3 py-2 text-sm font-medium rounded-md">
                        <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>Call Flows</a></li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1 ml-64">
            <!-- Page Header -->
            <div class="p-6 border-b border-gray-200 bg-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900">Call Flow Builder</h1>
                        <p class="mt-1 text-sm text-gray-500">Design intelligent IVR workflows with AI assistance</p>
                    </div>
                    <div class="flex space-x-3">
                        <button id="aiAssistBtn" class="bg-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-purple-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            AI Assistant
                        </button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200">
                            Preview
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                            Save Flow
                        </button>
                    </div>
                </div>
            </div>

            <div class="flex h-screen">
                <!-- SignalWire Nodes Toolbox -->
                <div class="w-80 bg-white border-r border-gray-200 p-6 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">SignalWire Nodes</h3>

                    <!-- Starting Nodes -->
                    <div class="node-category">
                        <div class="category-title">Starting</div>
                        <div class="sw-node border-green-200 bg-green-50" draggable="true" data-node-type="handle-call">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Handle Call</div>
                                    <div class="text-xs text-gray-500">Starting point for incoming calls</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Nodes -->
                    <div class="node-category">
                        <div class="category-title">Action</div>

                        <div class="sw-node border-blue-200 bg-blue-50" draggable="true" data-node-type="answer-call">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Answer Call</div>
                                    <div class="text-xs text-gray-500">Answers incoming call</div>
                                </div>
                            </div>
                        </div>

                        <div class="sw-node border-purple-200 bg-purple-50" draggable="true" data-node-type="play-audio-tts">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Play Audio or TTS</div>
                                    <div class="text-xs text-gray-500">Play audio file or text-to-speech</div>
                                </div>
                            </div>
                        </div>

                        <div class="sw-node border-red-200 bg-red-50" draggable="true" data-node-type="hangup-call">
                            <div class="node-connector input"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 3l18 18"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Hang Up Call</div>
                                    <div class="text-xs text-gray-500">Terminates the call</div>
                                </div>
                            </div>
                        </div>

                        <div class="sw-node border-yellow-200 bg-yellow-50" draggable="true" data-node-type="send-sms">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Send SMS</div>
                                    <div class="text-xs text-gray-500">Send SMS message</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Forwarding Nodes -->
                    <div class="node-category">
                        <div class="category-title">Forwarding</div>
                        <div class="sw-node border-orange-200 bg-orange-50" draggable="true" data-node-type="forward-to-phone">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Forward to Phone</div>
                                    <div class="text-xs text-gray-500">Forward call to phone number</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Input Nodes -->
                    <div class="node-category">
                        <div class="category-title">Input</div>

                        <div class="sw-node border-indigo-200 bg-indigo-50" draggable="true" data-node-type="gather-input">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Gather Input</div>
                                    <div class="text-xs text-gray-500">Collect DTMF or speech input</div>
                                </div>
                            </div>
                        </div>

                        <div class="sw-node border-pink-200 bg-pink-50" draggable="true" data-node-type="ai-agent">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-pink-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">AI Agent</div>
                                    <div class="text-xs text-gray-500">Connect to AI agent</div>
                                </div>
                            </div>
                        </div>

                        <div class="sw-node border-gray-200 bg-gray-50" draggable="true" data-node-type="request">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Request</div>
                                    <div class="text-xs text-gray-500">Send HTTP request</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decision Nodes -->
                    <div class="node-category">
                        <div class="category-title">Decision</div>

                        <div class="sw-node border-teal-200 bg-teal-50" draggable="true" data-node-type="conditions">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-teal-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Conditions</div>
                                    <div class="text-xs text-gray-500">Evaluate conditions and branch</div>
                                </div>
                            </div>
                        </div>

                        <div class="sw-node border-cyan-200 bg-cyan-50" draggable="true" data-node-type="set-variables">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-cyan-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Set Variables</div>
                                    <div class="text-xs text-gray-500">Set flow variables</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Templates -->
                    <div>
                        <h4 class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">AI Templates</h4>
                        <div class="space-y-2">
                            <button class="w-full text-left p-3 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg hover:from-purple-100 hover:to-pink-100">
                                <div class="text-sm font-medium text-gray-900">Customer Service</div>
                                <div class="text-xs text-gray-500">AI-powered support flow</div>
                            </button>
                            <button class="w-full text-left p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg hover:from-blue-100 hover:to-indigo-100">
                                <div class="text-sm font-medium text-gray-900">Sales Routing</div>
                                <div class="text-xs text-gray-500">Intelligent lead routing</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- SignalWire Canvas -->
                <div class="flex-1 flow-canvas relative overflow-hidden">
                    <div class="absolute inset-0 p-8" id="flowCanvas">
                        <!-- Default Handle Call Node -->
                        <div class="sw-node border-green-200 bg-green-50 selected" style="position: absolute; top: 100px; left: 100px;" data-node-type="handle-call" id="node-handle-call-1">
                            <div class="node-connector input"></div>
                            <div class="node-connector output"></div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="text-sm font-semibold text-gray-900">Handle Call</div>
                                    <div class="text-xs text-gray-500">Starting point</div>
                                </div>
                            </div>
                            <button class="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600" onclick="deleteNode(this.parentElement)">×</button>
                        </div>

                        <!-- Empty state message -->
                        <div class="absolute inset-0 flex items-center justify-center pointer-events-none" id="emptyState">
                            <div class="text-center">
                                <svg class="mx-auto h-16 w-16 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                <h3 class="mt-4 text-lg font-medium text-gray-400">SignalWire Call Flow Builder</h3>
                                <p class="mt-2 text-sm text-gray-400">Drag nodes from the toolbox to build your call flow</p>
                                <p class="mt-1 text-xs text-gray-400">Start with the Handle Call node already placed</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Properties & AI Panel -->
                <div class="w-96 bg-white border-l border-gray-200 flex flex-col">
                    <!-- Node Properties -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Node Properties</h3>
                        <div id="nodeProperties">
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <p class="mt-2 text-sm text-gray-500">Select a node to configure its properties</p>
                            </div>
                        </div>
                    </div>

                    <!-- AI Assistant -->
                    <div class="flex-1 p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            AI Assistant
                        </h4>

                        <div class="bg-gray-50 rounded-lg p-4 mb-4 h-48 overflow-y-auto" id="aiChat">
                            <div class="text-sm text-gray-600">
                                🤖 <strong>SignalWire AI Assistant</strong><br><br>
                                I can help you build call flows using natural language. Try these examples:<br><br>
                                • "Create a customer service IVR"<br>
                                • "Add a voicemail after 3 rings"<br>
                                • "Route calls based on time of day"<br>
                                • "Connect to an AI agent for support"
                            </div>
                        </div>

                        <div class="space-y-3">
                            <div class="flex">
                                <input type="text" id="aiInput" placeholder="Describe what you want to build..." class="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                <button id="aiSendBtn" class="bg-purple-600 text-white px-4 py-2 rounded-r-lg text-sm font-medium hover:bg-purple-700 transition-colors">
                                    Send
                                </button>
                            </div>

                            <!-- Quick AI Actions -->
                            <div class="grid grid-cols-2 gap-2">
                                <button class="ai-quick-btn bg-blue-50 text-blue-700 border border-blue-200 px-3 py-2 rounded-lg text-xs font-medium hover:bg-blue-100 transition-colors">
                                    🏢 Business Hours
                                </button>
                                <button class="ai-quick-btn bg-green-50 text-green-700 border border-green-200 px-3 py-2 rounded-lg text-xs font-medium hover:bg-green-100 transition-colors">
                                    📞 Call Center
                                </button>
                                <button class="ai-quick-btn bg-purple-50 text-purple-700 border border-purple-200 px-3 py-2 rounded-lg text-xs font-medium hover:bg-purple-100 transition-colors">
                                    🤖 AI Support
                                </button>
                                <button class="ai-quick-btn bg-orange-50 text-orange-700 border border-orange-200 px-3 py-2 rounded-lg text-xs font-medium hover:bg-orange-100 transition-colors">
                                    📧 Voicemail
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Global variables
        let selectedNode = null;
        let nodeCounter = 1;
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        function goHome() {
            window.location.href = 'spaas-professional.html';
        }

        // Initialize SignalWire Call Flow Builder
        document.addEventListener('DOMContentLoaded', function() {
            initializeBuilder();
            setupEventListeners();
            selectNode(document.getElementById('node-handle-call-1'));
        });

        function initializeBuilder() {
            // Hide empty state since we have a default node
            const emptyState = document.getElementById('emptyState');
            if (emptyState) emptyState.style.display = 'none';
        }

        function setupEventListeners() {
            // Drag and drop from toolbox
            document.querySelectorAll('.sw-node[draggable="true"]').forEach(node => {
                node.addEventListener('dragstart', handleDragStart);
            });

            // Canvas drop zone
            const canvas = document.getElementById('flowCanvas');
            canvas.addEventListener('dragover', handleDragOver);
            canvas.addEventListener('drop', handleDrop);

            // AI Assistant
            document.getElementById('aiSendBtn').addEventListener('click', handleAIMessage);
            document.getElementById('aiInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') handleAIMessage();
            });

            // Quick AI buttons
            document.querySelectorAll('.ai-quick-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const prompt = this.textContent.trim();
                    document.getElementById('aiInput').value = `Create a ${prompt.toLowerCase()} call flow`;
                    handleAIMessage();
                });
            });

            // Header buttons
            setupHeaderButtons();
        }

        function setupHeaderButtons() {
            document.getElementById('aiAssistBtn').addEventListener('click', function() {
                alert('🤖 SignalWire AI Assistant\n\nPowered by ChatGPT integration:\n\n✓ Natural language flow generation\n✓ SWML code generation\n✓ Best practices suggestions\n✓ Real-time assistance\n\nTry: "Create a customer service IVR with menu options"');
            });

            const saveBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Save Flow'));
            const previewBtn = Array.from(document.querySelectorAll('button')).find(btn => btn.textContent.includes('Preview'));

            if (saveBtn) {
                saveBtn.addEventListener('click', function() {
                    const flowData = generateSWML();
                    alert(`💾 Save SignalWire Call Flow\n\n✓ SWML generated and validated\n✓ Multi-tenant isolation applied\n✓ Version control enabled\n✓ Edge Function ready\n\nFlow saved to database with tenant isolation.`);
                });
            }

            if (previewBtn) {
                previewBtn.addEventListener('click', function() {
                    alert('👁️ Preview Call Flow\n\n✓ Interactive SWML simulation\n✓ Test call routing logic\n✓ Validate node connections\n✓ Audio/TTS preview\n✓ Real-time debugging\n\nOpening SignalWire flow simulator...');
                });
            }
        }

        // Drag and Drop functionality
        function handleDragStart(e) {
            const nodeType = e.target.closest('.sw-node').dataset.nodeType;
            e.dataTransfer.setData('text/plain', nodeType);
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDrop(e) {
            e.preventDefault();
            const nodeType = e.dataTransfer.getData('text/plain');
            const rect = e.currentTarget.getBoundingClientRect();
            const x = e.clientX - rect.left - 100; // Offset for node width
            const y = e.clientY - rect.top - 50;   // Offset for node height

            createNode(nodeType, x, y);
        }

        function createNode(nodeType, x, y) {
            nodeCounter++;
            const nodeId = `node-${nodeType}-${nodeCounter}`;

            const nodeConfig = getNodeConfig(nodeType);
            const nodeElement = document.createElement('div');
            nodeElement.className = `sw-node ${nodeConfig.borderColor} ${nodeConfig.bgColor}`;
            nodeElement.style.position = 'absolute';
            nodeElement.style.left = x + 'px';
            nodeElement.style.top = y + 'px';
            nodeElement.dataset.nodeType = nodeType;
            nodeElement.id = nodeId;

            nodeElement.innerHTML = `
                <div class="node-connector input"></div>
                ${nodeConfig.hasOutput ? '<div class="node-connector output"></div>' : ''}
                <div class="flex items-center">
                    <div class="w-10 h-10 ${nodeConfig.iconBg} rounded-lg flex items-center justify-center mr-3">
                        ${nodeConfig.icon}
                    </div>
                    <div>
                        <div class="text-sm font-semibold text-gray-900">${nodeConfig.title}</div>
                        <div class="text-xs text-gray-500">${nodeConfig.subtitle}</div>
                    </div>
                </div>
                <button class="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600" onclick="deleteNode(this.parentElement)">×</button>
            `;

            // Add click handler
            nodeElement.addEventListener('click', () => selectNode(nodeElement));

            document.getElementById('flowCanvas').appendChild(nodeElement);
            selectNode(nodeElement);
        }

        function getNodeConfig(nodeType) {
            const configs = {
                'handle-call': {
                    title: 'Handle Call',
                    subtitle: 'Starting point',
                    icon: '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>',
                    iconBg: 'bg-green-500',
                    borderColor: 'border-green-200',
                    bgColor: 'bg-green-50',
                    hasOutput: true
                },
                'answer-call': {
                    title: 'Answer Call',
                    subtitle: 'Answers incoming call',
                    icon: '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>',
                    iconBg: 'bg-blue-500',
                    borderColor: 'border-blue-200',
                    bgColor: 'bg-blue-50',
                    hasOutput: true
                },
                'play-audio-tts': {
                    title: 'Play Audio or TTS',
                    subtitle: 'Play audio/text-to-speech',
                    icon: '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"></path></svg>',
                    iconBg: 'bg-purple-500',
                    borderColor: 'border-purple-200',
                    bgColor: 'bg-purple-50',
                    hasOutput: true
                },
                'gather-input': {
                    title: 'Gather Input',
                    subtitle: 'Collect DTMF/speech',
                    icon: '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z"></path></svg>',
                    iconBg: 'bg-indigo-500',
                    borderColor: 'border-indigo-200',
                    bgColor: 'bg-indigo-50',
                    hasOutput: true
                },
                'ai-agent': {
                    title: 'AI Agent',
                    subtitle: 'Connect to AI agent',
                    icon: '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path></svg>',
                    iconBg: 'bg-pink-500',
                    borderColor: 'border-pink-200',
                    bgColor: 'bg-pink-50',
                    hasOutput: true
                },
                'hangup-call': {
                    title: 'Hang Up Call',
                    subtitle: 'Terminates the call',
                    icon: '<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M3 3l18 18"></path></svg>',
                    iconBg: 'bg-red-500',
                    borderColor: 'border-red-200',
                    bgColor: 'bg-red-50',
                    hasOutput: false
                }
            };

            return configs[nodeType] || configs['handle-call'];
        }

        function selectNode(nodeElement) {
            // Remove selection from all nodes
            document.querySelectorAll('.sw-node').forEach(node => {
                node.classList.remove('selected');
            });

            // Select current node
            nodeElement.classList.add('selected');
            selectedNode = nodeElement;

            // Update properties panel
            updatePropertiesPanel(nodeElement);
        }

        function updatePropertiesPanel(nodeElement) {
            const nodeType = nodeElement.dataset.nodeType;
            const propertiesDiv = document.getElementById('nodeProperties');

            const properties = getNodeProperties(nodeType);
            propertiesDiv.innerHTML = `
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-semibold text-gray-900 mb-2">${properties.title}</h4>
                        <p class="text-xs text-gray-500 mb-4">${properties.description}</p>
                    </div>
                    ${properties.fields.map(field => `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">${field.label}</label>
                            ${field.type === 'textarea' ?
                                `<textarea class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="${field.placeholder}">${field.value || ''}</textarea>` :
                                `<input type="${field.type}" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="${field.placeholder}" value="${field.value || ''}">`
                            }
                        </div>
                    `).join('')}
                    <div class="pt-4 border-t border-gray-200">
                        <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700">
                            Update Node
                        </button>
                    </div>
                </div>
            `;
        }

        function getNodeProperties(nodeType) {
            const properties = {
                'handle-call': {
                    title: 'Handle Call Node',
                    description: 'Entry point for incoming calls. This node is automatically triggered when a call is received.',
                    fields: [
                        { label: 'Node Name', type: 'text', placeholder: 'Handle Call', value: 'Handle Call' }
                    ]
                },
                'play-audio-tts': {
                    title: 'Play Audio or TTS',
                    description: 'Play an audio file or convert text to speech for the caller.',
                    fields: [
                        { label: 'Content Type', type: 'select', options: ['Text-to-Speech', 'Audio File'], value: 'Text-to-Speech' },
                        { label: 'Text Content', type: 'textarea', placeholder: 'Welcome to our service. Please hold while we connect you.', value: '' },
                        { label: 'Voice', type: 'select', options: ['alice', 'man', 'woman'], value: 'alice' },
                        { label: 'Language', type: 'select', options: ['en-US', 'en-GB', 'es-ES'], value: 'en-US' }
                    ]
                },
                'gather-input': {
                    title: 'Gather Input',
                    description: 'Collect DTMF tones or speech input from the caller.',
                    fields: [
                        { label: 'Input Type', type: 'select', options: ['DTMF', 'Speech', 'Both'], value: 'DTMF' },
                        { label: 'Max Digits', type: 'number', placeholder: '1', value: '1' },
                        { label: 'Timeout (seconds)', type: 'number', placeholder: '5', value: '5' },
                        { label: 'Prompt Text', type: 'textarea', placeholder: 'Press 1 for sales, 2 for support, or 0 for operator', value: '' }
                    ]
                },
                'ai-agent': {
                    title: 'AI Agent',
                    description: 'Connect the caller to an AI-powered conversational agent.',
                    fields: [
                        { label: 'Agent Name', type: 'text', placeholder: 'Customer Support Agent', value: '' },
                        { label: 'AI Model', type: 'select', options: ['GPT-4', 'GPT-3.5', 'Claude'], value: 'GPT-4' },
                        { label: 'System Prompt', type: 'textarea', placeholder: 'You are a helpful customer support agent...', value: '' },
                        { label: 'Max Duration (minutes)', type: 'number', placeholder: '10', value: '10' }
                    ]
                }
            };

            return properties[nodeType] || properties['handle-call'];
        }

        function deleteNode(nodeElement) {
            if (nodeElement.id === 'node-handle-call-1') {
                alert('Cannot delete the Handle Call node - it\'s required as the entry point.');
                return;
            }

            if (confirm('Delete this node?')) {
                nodeElement.remove();

                // Clear properties if this was the selected node
                if (selectedNode === nodeElement) {
                    selectedNode = null;
                    document.getElementById('nodeProperties').innerHTML = `
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <p class="mt-2 text-sm text-gray-500">Select a node to configure its properties</p>
                        </div>
                    `;
                }
            }
        }

        function handleAIMessage() {
            const input = document.getElementById('aiInput');
            const chat = document.getElementById('aiChat');

            if (!input.value.trim()) return;

            // Add user message
            chat.innerHTML += `
                <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <div class="text-sm font-medium text-blue-900">You:</div>
                    <div class="text-sm text-blue-800 mt-1">${input.value}</div>
                </div>
            `;

            // Simulate AI response
            setTimeout(() => {
                const responses = [
                    "I'll create that SignalWire call flow for you! 🤖\n\n✓ Analyzing your requirements\n✓ Generating SWML structure\n✓ Adding appropriate nodes\n✓ Configuring connections\n\nFlow components added to canvas!",
                    "Great idea! Here's what I'll build:\n\n🎯 Handle Call (entry point)\n📢 Play welcome message\n🔢 Gather DTMF input\n🤖 AI Agent for complex queries\n📞 Forward to appropriate department\n\nDrag nodes from toolbox to customize!",
                    "Perfect! I'm generating a professional call flow:\n\n✅ Business hours detection\n✅ Menu options (1-9)\n✅ Voicemail fallback\n✅ Call recording\n✅ Analytics tracking\n\nReady for SignalWire deployment!"
                ];

                const randomResponse = responses[Math.floor(Math.random() * responses.length)];

                chat.innerHTML += `
                    <div class="mb-4 p-3 bg-purple-50 rounded-lg">
                        <div class="text-sm font-medium text-purple-900">🤖 SignalWire AI:</div>
                        <div class="text-sm text-purple-800 mt-1 whitespace-pre-line">${randomResponse}</div>
                    </div>
                `;
                chat.scrollTop = chat.scrollHeight;
            }, 1500);

            input.value = '';
            chat.scrollTop = chat.scrollHeight;
        }

        function generateSWML() {
            // This would generate actual SWML based on the flow
            return {
                version: "1.0.0",
                sections: {
                    main: [
                        {
                            answer: {}
                        },
                        {
                            play: {
                                url: "say:Welcome to our service"
                            }
                        }
                    ]
                }
            };
        }
    </script>
</body>
</html>
