(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1882:(e,s,t)=>{Promise.resolve().then(t.bind(t,2868))},2868:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var l=t(5155),a=t(8999);function r(){let e=(0,a.useRouter)(),s=async e=>{if("email"===e){let e=prompt("Enter your email:");if(!e)return;alert("\uD83D\uDE80 SPaaS Platform Demo\n\nIn production, a magic link would be sent to:\n".concat(e,'\n\nFor now, click "Dashboard Demo" to see the admin interface!'));return}alert("\uD83D\uDE80 SPaaS Platform Demo\n\n✅ ".concat(e.toUpperCase()," OAuth Integration Ready\n✅ Supabase Authentication\n✅ Multi-tenant Architecture\n✅ Row-Level Security\n\nIn production, you would be redirected to ").concat(e,' login.\n\nClick "Dashboard Demo" to see the admin interface!'))},t=()=>{e.push("/dashboard")};return(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50",children:[(0,l.jsx)("header",{className:"bg-white/80 backdrop-blur-sm border-b",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"SPaaS Platform"}),(0,l.jsx)("span",{className:"ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:"Beta"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("button",{onClick:()=>s("email"),className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50",children:"Sign In"}),(0,l.jsx)("button",{onClick:t,className:"px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700",children:"Dashboard Demo"}),(0,l.jsx)("button",{onClick:()=>s("google"),className:"px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700",children:"Get Started"})]})]})})}),(0,l.jsx)("section",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 mb-6",children:"SIP Platform as a Service"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Complete multi-tenant telecommunications platform with AI-powered call flows, real-time analytics, and seamless integrations. Built for resellers and enterprises."}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,l.jsx)("button",{onClick:()=>s("google"),className:"px-8 py-3 bg-blue-600 text-white rounded-md text-lg font-medium hover:bg-blue-700",children:"Start Free Trial"}),(0,l.jsx)("button",{onClick:t,className:"px-8 py-3 bg-green-600 text-white rounded-md text-lg font-medium hover:bg-green-700",children:"Dashboard Demo"}),(0,l.jsx)("button",{onClick:()=>alert('\uD83D\uDE80 SPaaS Platform Features:\n\n✅ Multi-tenant architecture\n✅ SIP/VoIP management\n✅ AI-powered IVR\n✅ Real-time analytics\n✅ Enterprise security\n✅ Global integrations\n\nClick "Dashboard Demo" to see the admin interface!'),className:"px-8 py-3 border border-gray-300 text-gray-700 rounded-md text-lg font-medium hover:bg-gray-50",children:"View Features"})]})]})}),(0,l.jsxs)("section",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Everything you need for modern telephony"}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"Powerful features designed for scalability and ease of use"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:(0,l.jsx)("div",{className:"w-6 h-6 bg-blue-600 rounded"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Multi-Tenant Architecture"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Complete tenant isolation with white-label capabilities and custom branding"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4",children:(0,l.jsx)("div",{className:"w-6 h-6 bg-green-600 rounded"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"SIP & VoIP Management"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Full SIP account management with DID numbers, call routing, and recording"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4",children:(0,l.jsx)("div",{className:"w-6 h-6 bg-purple-600 rounded"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"AI-Powered IVR"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Visual workflow builder with ChatGPT integration for intelligent call flows"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4",children:(0,l.jsx)("div",{className:"w-6 h-6 bg-orange-600 rounded"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Real-time Analytics"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Comprehensive reporting with CDR analysis and usage statistics"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4",children:(0,l.jsx)("div",{className:"w-6 h-6 bg-red-600 rounded"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Enterprise Security"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Row-level security, JWT authentication, and comprehensive audit logging"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4",children:(0,l.jsx)("div",{className:"w-6 h-6 bg-indigo-600 rounded"})}),(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Global Integrations"}),(0,l.jsx)("p",{className:"text-gray-600",children:"SignalWire, Twilio, Stripe integration with webhook support"})]})]})]}),(0,l.jsxs)("section",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:[(0,l.jsxs)("div",{className:"text-center mb-16",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Robust Database Architecture"}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"15 interconnected tables with full multi-tenancy support"})]}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"Core Tables"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,l.jsx)("li",{children:"• Tenants (Organizations)"}),(0,l.jsx)("li",{children:"• Users (Multi-role)"}),(0,l.jsx)("li",{children:"• Clients (End customers)"})]})]}),(0,l.jsxs)("div",{className:"border-l-4 border-green-500 pl-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"Telephony"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,l.jsx)("li",{children:"• SIP Accounts"}),(0,l.jsx)("li",{children:"• DID Numbers"}),(0,l.jsx)("li",{children:"• Call Flows (IVR)"}),(0,l.jsx)("li",{children:"• Call Detail Records"})]})]}),(0,l.jsxs)("div",{className:"border-l-4 border-purple-500 pl-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900",children:"Business"}),(0,l.jsxs)("ul",{className:"text-sm text-gray-600 mt-2 space-y-1",children:[(0,l.jsx)("li",{children:"• Invoices & Billing"}),(0,l.jsx)("li",{children:"• Support Tickets"}),(0,l.jsx)("li",{children:"• Audit Logs"}),(0,l.jsx)("li",{children:"• Webhooks"})]})]})]})})]}),(0,l.jsx)("section",{className:"bg-gray-900 text-white py-20",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,l.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Ready to transform your telecommunications business?"}),(0,l.jsx)("p",{className:"text-xl text-gray-300 mb-8",children:"Join hundreds of companies already using SPaaS Platform"}),(0,l.jsx)("button",{onClick:()=>s("google"),className:"px-8 py-3 bg-white text-gray-900 rounded-md text-lg font-medium hover:bg-gray-100",children:"Start Your Free Trial"})]})}),(0,l.jsx)("footer",{className:"bg-white border-t",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:(0,l.jsxs)("div",{className:"text-center text-gray-600",children:[(0,l.jsx)("p",{children:"\xa9 2024 SPaaS Platform. Built with Supabase, Next.js, and modern web technologies."}),(0,l.jsx)("p",{className:"mt-2 text-sm",children:"Complete source code available • Multi-tenant • Production-ready"})]})})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(1882)),_N_E=e.O()}]);