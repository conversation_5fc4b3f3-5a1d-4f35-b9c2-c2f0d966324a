-- SPaaS Platform Triggers and Functions
-- Automatic timestamp updates, logging, and JWT claims

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON> updated_at triggers for all tables with updated_at column
CREATE TRIGGER update_tenants_updated_at
    BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clients_updated_at
    BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sip_accounts_updated_at
    BEFORE UPDATE ON sip_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_did_numbers_updated_at
    BEFORE UPDATE ON did_numbers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_call_flows_updated_at
    BEFORE UPDATE ON call_flows
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tickets_updated_at
    BEFORE UPDATE ON tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at
    BEFORE UPDATE ON invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at
    BEFORE UPDATE ON api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pricing_tiers_updated_at
    BEFORE UPDATE ON pricing_tiers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at
    BEFORE UPDATE ON subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_prompt_templates_updated_at
    BEFORE UPDATE ON prompt_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically log user actions for tenants table
CREATE OR REPLACE FUNCTION log_tenant_action()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO logs (
        tenant_id,
        user_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        auth.uid(),
        TG_OP,
        'tenants',
        COALESCE(NEW.id, OLD.id),
        jsonb_build_object(
            'old', to_jsonb(OLD),
            'new', to_jsonb(NEW)
        )
    );

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically log user actions for users table
CREATE OR REPLACE FUNCTION log_user_action()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO logs (
        tenant_id,
        user_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        COALESCE(NEW.tenant_id, OLD.tenant_id),
        auth.uid(),
        TG_OP,
        'users',
        COALESCE(NEW.id, OLD.id),
        jsonb_build_object(
            'old', to_jsonb(OLD),
            'new', to_jsonb(NEW)
        )
    );

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically log client actions
CREATE OR REPLACE FUNCTION log_client_action()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO logs (
        tenant_id,
        user_id,
        action,
        resource_type,
        resource_id,
        details
    ) VALUES (
        COALESCE(NEW.tenant_id, OLD.tenant_id),
        auth.uid(),
        TG_OP,
        'clients',
        COALESCE(NEW.id, OLD.id),
        jsonb_build_object(
            'old', to_jsonb(OLD),
            'new', to_jsonb(NEW)
        )
    );

    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create audit triggers for important tables
CREATE TRIGGER audit_tenants
    AFTER INSERT OR UPDATE OR DELETE ON tenants
    FOR EACH ROW EXECUTE FUNCTION log_tenant_action();

CREATE TRIGGER audit_users
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION log_user_action();

CREATE TRIGGER audit_clients
    AFTER INSERT OR UPDATE OR DELETE ON clients
    FOR EACH ROW EXECUTE FUNCTION log_client_action();

-- Function to handle user registration and set JWT claims
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert into users table with basic info
    INSERT INTO users (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        'client'
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to add custom claims to JWT
CREATE OR REPLACE FUNCTION add_custom_claims(user_id UUID)
RETURNS JSONB AS $$
DECLARE
    user_record RECORD;
    claims JSONB;
BEGIN
    SELECT u.tenant_id, u.role, t.slug as tenant_slug
    INTO user_record
    FROM users u
    LEFT JOIN tenants t ON u.tenant_id = t.id
    WHERE u.id = user_id;

    claims := jsonb_build_object(
        'tenant_id', user_record.tenant_id,
        'role', user_record.role,
        'tenant_slug', user_record.tenant_slug
    );

    RETURN claims;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate API key
CREATE OR REPLACE FUNCTION generate_api_key(
    p_tenant_id UUID,
    p_name VARCHAR(255),
    p_permissions TEXT[] DEFAULT ARRAY[]::TEXT[]
)
RETURNS TABLE(api_key TEXT, key_id UUID) AS $$
DECLARE
    key_uuid UUID;
    key_string TEXT;
    key_prefix TEXT;
    key_hash TEXT;
BEGIN
    key_uuid := uuid_generate_v4();
    key_prefix := 'sk_' || substring(key_uuid::TEXT, 1, 8);
    key_string := key_prefix || '_' || encode(gen_random_bytes(32), 'base64');
    key_hash := encode(digest(key_string, 'sha256'), 'hex');

    INSERT INTO api_keys (
        id,
        tenant_id,
        created_by,
        name,
        key_hash,
        key_prefix,
        permissions
    ) VALUES (
        key_uuid,
        p_tenant_id,
        auth.uid(),
        p_name,
        key_hash,
        key_prefix,
        p_permissions
    );

    RETURN QUERY SELECT key_string, key_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;